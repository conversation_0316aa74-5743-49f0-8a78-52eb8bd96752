package com.example.castapp.model;

/**
 * 🎯 统一的窗口参数管理类
 * 用于在遥控端统一管理所有窗口参数，避免分散存储和复杂收集
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0017\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\bO\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\b\u0018\u0000 {2\u00020\u0001:\u0001{B\u0091\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\b\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u0006\u0010\r\u001a\u00020\f\u0012\u0006\u0010\u000e\u001a\u00020\f\u0012\u0006\u0010\u000f\u001a\u00020\f\u0012\u0006\u0010\u0010\u001a\u00020\u0006\u0012\u0006\u0010\u0011\u001a\u00020\b\u0012\u0006\u0010\u0012\u001a\u00020\b\u0012\u0006\u0010\u0013\u001a\u00020\b\u0012\u0006\u0010\u0014\u001a\u00020\b\u0012\u0006\u0010\u0015\u001a\u00020\b\u0012\u0006\u0010\u0016\u001a\u00020\b\u0012\u0006\u0010\u0017\u001a\u00020\f\u0012\u0006\u0010\u0018\u001a\u00020\f\u0012\u0006\u0010\u0019\u001a\u00020\b\u0012\u0006\u0010\u001a\u001a\u00020\u0006\u0012\u0006\u0010\u001b\u001a\u00020\f\u0012\u0006\u0010\u001c\u001a\u00020\b\u0012\u0006\u0010\u001d\u001a\u00020\b\u0012\u0006\u0010\u001e\u001a\u00020\u0006\u0012\u0006\u0010\u001f\u001a\u00020\u0006\u0012\u0006\u0010 \u001a\u00020\b\u0012\u0006\u0010!\u001a\u00020\u0006\u0012\u0006\u0010\"\u001a\u00020\b\u0012\n\b\u0002\u0010#\u001a\u0004\u0018\u00010$\u0012\b\b\u0002\u0010%\u001a\u00020\u0003\u0012\b\b\u0002\u0010&\u001a\u00020\'\u00a2\u0006\u0002\u0010(J\t\u0010R\u001a\u00020\u0003H\u00c6\u0003J\t\u0010S\u001a\u00020\fH\u00c6\u0003J\t\u0010T\u001a\u00020\u0006H\u00c6\u0003J\t\u0010U\u001a\u00020\bH\u00c6\u0003J\t\u0010V\u001a\u00020\bH\u00c6\u0003J\t\u0010W\u001a\u00020\bH\u00c6\u0003J\t\u0010X\u001a\u00020\bH\u00c6\u0003J\t\u0010Y\u001a\u00020\bH\u00c6\u0003J\t\u0010Z\u001a\u00020\bH\u00c6\u0003J\t\u0010[\u001a\u00020\fH\u00c6\u0003J\t\u0010\\\u001a\u00020\fH\u00c6\u0003J\t\u0010]\u001a\u00020\u0003H\u00c6\u0003J\t\u0010^\u001a\u00020\bH\u00c6\u0003J\t\u0010_\u001a\u00020\u0006H\u00c6\u0003J\t\u0010`\u001a\u00020\fH\u00c6\u0003J\t\u0010a\u001a\u00020\bH\u00c6\u0003J\t\u0010b\u001a\u00020\bH\u00c6\u0003J\t\u0010c\u001a\u00020\u0006H\u00c6\u0003J\t\u0010d\u001a\u00020\u0006H\u00c6\u0003J\t\u0010e\u001a\u00020\bH\u00c6\u0003J\t\u0010f\u001a\u00020\u0006H\u00c6\u0003J\t\u0010g\u001a\u00020\bH\u00c6\u0003J\t\u0010h\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010i\u001a\u0004\u0018\u00010$H\u00c6\u0003J\t\u0010j\u001a\u00020\u0003H\u00c6\u0003J\t\u0010k\u001a\u00020\'H\u00c6\u0003J\t\u0010l\u001a\u00020\bH\u00c6\u0003J\u000b\u0010m\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010n\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010o\u001a\u00020\fH\u00c6\u0003J\t\u0010p\u001a\u00020\fH\u00c6\u0003J\t\u0010q\u001a\u00020\fH\u00c6\u0003J\u00cf\u0002\u0010r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\f2\b\b\u0002\u0010\u000f\u001a\u00020\f2\b\b\u0002\u0010\u0010\u001a\u00020\u00062\b\b\u0002\u0010\u0011\u001a\u00020\b2\b\b\u0002\u0010\u0012\u001a\u00020\b2\b\b\u0002\u0010\u0013\u001a\u00020\b2\b\b\u0002\u0010\u0014\u001a\u00020\b2\b\b\u0002\u0010\u0015\u001a\u00020\b2\b\b\u0002\u0010\u0016\u001a\u00020\b2\b\b\u0002\u0010\u0017\u001a\u00020\f2\b\b\u0002\u0010\u0018\u001a\u00020\f2\b\b\u0002\u0010\u0019\u001a\u00020\b2\b\b\u0002\u0010\u001a\u001a\u00020\u00062\b\b\u0002\u0010\u001b\u001a\u00020\f2\b\b\u0002\u0010\u001c\u001a\u00020\b2\b\b\u0002\u0010\u001d\u001a\u00020\b2\b\b\u0002\u0010\u001e\u001a\u00020\u00062\b\b\u0002\u0010\u001f\u001a\u00020\u00062\b\b\u0002\u0010 \u001a\u00020\b2\b\b\u0002\u0010!\u001a\u00020\u00062\b\b\u0002\u0010\"\u001a\u00020\b2\n\b\u0002\u0010#\u001a\u0004\u0018\u00010$2\b\b\u0002\u0010%\u001a\u00020\u00032\b\b\u0002\u0010&\u001a\u00020\'H\u00c6\u0001J\u0013\u0010s\u001a\u00020\b2\b\u0010t\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010u\u001a\u00020\u0006H\u00d6\u0001J\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010wJ\u0006\u0010x\u001a\u00020yJ\t\u0010z\u001a\u00020\u0003H\u00d6\u0001R\u001a\u0010\u0018\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b)\u0010*\"\u0004\b+\u0010,R\u0011\u0010\u001f\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010.R\u0011\u0010\u001e\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010.R\u001a\u0010\u001a\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b0\u0010.\"\u0004\b1\u00102R\u001a\u0010\u001b\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b3\u0010*\"\u0004\b4\u0010,R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u00106R\u001a\u0010\u0017\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b7\u0010*\"\u0004\b8\u0010,R\u0013\u0010#\u001a\u0004\u0018\u00010$\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010:R\u0011\u0010%\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u00106R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u00106R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u00106R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010>R\u001a\u0010\u0019\u001a\u00020\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0019\u0010>\"\u0004\b?\u0010@R\u0011\u0010\u001c\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010>R\u0011\u0010\u0011\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010>R\u0011\u0010\u0012\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010>R\u0011\u0010\u001d\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010>R\u0011\u0010\"\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010>R\u001a\u0010\u0016\u001a\u00020\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0016\u0010>\"\u0004\bA\u0010@R\u0011\u0010\u0014\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010>R\u0011\u0010\u0013\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010>R\u001a\u0010\u0015\u001a\u00020\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010>\"\u0004\bB\u0010@R\u0011\u0010&\u001a\u00020\'\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010DR\u0013\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u00106R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010.R\u001a\u0010\u000b\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bG\u0010*\"\u0004\bH\u0010,R\u001a\u0010\r\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bI\u0010*\"\u0004\bJ\u0010,R\u001a\u0010\u000f\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bK\u0010*\"\u0004\bL\u0010,R\u001a\u0010\u000e\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bM\u0010*\"\u0004\bN\u0010,R\u0011\u0010!\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bO\u0010.R\u0011\u0010 \u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\bP\u0010>R\u0011\u0010\u0010\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bQ\u0010.\u00a8\u0006|"}, d2 = {"Lcom/example/castapp/model/RemoteWindowConfig;", "", "connectionId", "", "ipAddress", "port", "", "isActive", "", "deviceName", "note", "positionX", "", "positionY", "scaleFactor", "rotationAngle", "zOrder", "isCropping", "isDragEnabled", "isScaleEnabled", "isRotationEnabled", "isVisible", "isMirrored", "cornerRadius", "alpha", "isBorderEnabled", "borderColor", "borderWidth", "isControlEnabled", "isEditEnabled", "baseWindowWidth", "baseWindowHeight", "windowColorEnabled", "windowBackgroundColor", "isLandscapeModeEnabled", "cropRectRatio", "Landroid/graphics/RectF;", "dataSource", "lastUpdated", "", "(Ljava/lang/String;Ljava/lang/String;IZLjava/lang/String;Ljava/lang/String;FFFFIZZZZZZFFZIFZZIIZIZLandroid/graphics/RectF;Ljava/lang/String;J)V", "getAlpha", "()F", "setAlpha", "(F)V", "getBaseWindowHeight", "()I", "getBaseWindowWidth", "getBorderColor", "setBorderColor", "(I)V", "getBorderWidth", "setBorderWidth", "getConnectionId", "()Ljava/lang/String;", "getCornerRadius", "setCornerRadius", "getCropRectRatio", "()Landroid/graphics/RectF;", "getDataSource", "getDeviceName", "getIpAddress", "()Z", "setBorderEnabled", "(Z)V", "setMirrored", "setVisible", "getLastUpdated", "()J", "getNote", "getPort", "getPositionX", "setPositionX", "getPositionY", "setPositionY", "getRotationAngle", "setRotationAngle", "getScaleFactor", "setScaleFactor", "getWindowBackgroundColor", "getWindowColorEnabled", "getZOrder", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toBatchSyncData", "", "toCastWindowInfo", "Lcom/example/castapp/model/CastWindowInfo;", "toString", "Companion", "app_debug"})
public final class RemoteWindowConfig {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String connectionId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String ipAddress = null;
    private final int port = 0;
    private final boolean isActive = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String deviceName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String note = null;
    private float positionX;
    private float positionY;
    private float scaleFactor;
    private float rotationAngle;
    private final int zOrder = 0;
    private final boolean isCropping = false;
    private final boolean isDragEnabled = false;
    private final boolean isScaleEnabled = false;
    private final boolean isRotationEnabled = false;
    private boolean isVisible;
    private boolean isMirrored;
    private float cornerRadius;
    private float alpha;
    private boolean isBorderEnabled;
    private int borderColor;
    private float borderWidth;
    private final boolean isControlEnabled = false;
    private final boolean isEditEnabled = false;
    private final int baseWindowWidth = 0;
    private final int baseWindowHeight = 0;
    private final boolean windowColorEnabled = false;
    private final int windowBackgroundColor = 0;
    private final boolean isLandscapeModeEnabled = false;
    @org.jetbrains.annotations.Nullable()
    private final android.graphics.RectF cropRectRatio = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String dataSource = null;
    private final long lastUpdated = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.model.RemoteWindowConfig.Companion Companion = null;
    
    public RemoteWindowConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, boolean isActive, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.Nullable()
    java.lang.String note, float positionX, float positionY, float scaleFactor, float rotationAngle, int zOrder, boolean isCropping, boolean isDragEnabled, boolean isScaleEnabled, boolean isRotationEnabled, boolean isVisible, boolean isMirrored, float cornerRadius, float alpha, boolean isBorderEnabled, int borderColor, float borderWidth, boolean isControlEnabled, boolean isEditEnabled, int baseWindowWidth, int baseWindowHeight, boolean windowColorEnabled, int windowBackgroundColor, boolean isLandscapeModeEnabled, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, @org.jetbrains.annotations.NotNull()
    java.lang.String dataSource, long lastUpdated) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getConnectionId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getIpAddress() {
        return null;
    }
    
    public final int getPort() {
        return 0;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDeviceName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getNote() {
        return null;
    }
    
    public final float getPositionX() {
        return 0.0F;
    }
    
    public final void setPositionX(float p0) {
    }
    
    public final float getPositionY() {
        return 0.0F;
    }
    
    public final void setPositionY(float p0) {
    }
    
    public final float getScaleFactor() {
        return 0.0F;
    }
    
    public final void setScaleFactor(float p0) {
    }
    
    public final float getRotationAngle() {
        return 0.0F;
    }
    
    public final void setRotationAngle(float p0) {
    }
    
    public final int getZOrder() {
        return 0;
    }
    
    public final boolean isCropping() {
        return false;
    }
    
    public final boolean isDragEnabled() {
        return false;
    }
    
    public final boolean isScaleEnabled() {
        return false;
    }
    
    public final boolean isRotationEnabled() {
        return false;
    }
    
    public final boolean isVisible() {
        return false;
    }
    
    public final void setVisible(boolean p0) {
    }
    
    public final boolean isMirrored() {
        return false;
    }
    
    public final void setMirrored(boolean p0) {
    }
    
    public final float getCornerRadius() {
        return 0.0F;
    }
    
    public final void setCornerRadius(float p0) {
    }
    
    public final float getAlpha() {
        return 0.0F;
    }
    
    public final void setAlpha(float p0) {
    }
    
    public final boolean isBorderEnabled() {
        return false;
    }
    
    public final void setBorderEnabled(boolean p0) {
    }
    
    public final int getBorderColor() {
        return 0;
    }
    
    public final void setBorderColor(int p0) {
    }
    
    public final float getBorderWidth() {
        return 0.0F;
    }
    
    public final void setBorderWidth(float p0) {
    }
    
    public final boolean isControlEnabled() {
        return false;
    }
    
    public final boolean isEditEnabled() {
        return false;
    }
    
    public final int getBaseWindowWidth() {
        return 0;
    }
    
    public final int getBaseWindowHeight() {
        return 0;
    }
    
    public final boolean getWindowColorEnabled() {
        return false;
    }
    
    public final int getWindowBackgroundColor() {
        return 0;
    }
    
    public final boolean isLandscapeModeEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.RectF getCropRectRatio() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDataSource() {
        return null;
    }
    
    public final long getLastUpdated() {
        return 0L;
    }
    
    /**
     * 🔄 转换为批量同步消息的数据格式
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> toBatchSyncData() {
        return null;
    }
    
    /**
     * 🔄 转换为CastWindowInfo
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.CastWindowInfo toCastWindowInfo() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    public final int component11() {
        return 0;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final boolean component16() {
        return false;
    }
    
    public final boolean component17() {
        return false;
    }
    
    public final float component18() {
        return 0.0F;
    }
    
    public final float component19() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component20() {
        return false;
    }
    
    public final int component21() {
        return 0;
    }
    
    public final float component22() {
        return 0.0F;
    }
    
    public final boolean component23() {
        return false;
    }
    
    public final boolean component24() {
        return false;
    }
    
    public final int component25() {
        return 0;
    }
    
    public final int component26() {
        return 0;
    }
    
    public final boolean component27() {
        return false;
    }
    
    public final int component28() {
        return 0;
    }
    
    public final boolean component29() {
        return false;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.RectF component30() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component31() {
        return null;
    }
    
    public final long component32() {
        return 0L;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    public final float component7() {
        return 0.0F;
    }
    
    public final float component8() {
        return 0.0F;
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.RemoteWindowConfig copy(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, boolean isActive, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.Nullable()
    java.lang.String note, float positionX, float positionY, float scaleFactor, float rotationAngle, int zOrder, boolean isCropping, boolean isDragEnabled, boolean isScaleEnabled, boolean isRotationEnabled, boolean isVisible, boolean isMirrored, float cornerRadius, float alpha, boolean isBorderEnabled, int borderColor, float borderWidth, boolean isControlEnabled, boolean isEditEnabled, int baseWindowWidth, int baseWindowHeight, boolean windowColorEnabled, int windowBackgroundColor, boolean isLandscapeModeEnabled, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, @org.jetbrains.annotations.NotNull()
    java.lang.String dataSource, long lastUpdated) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * 🔄 从CastWindowInfo创建统一参数
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/model/RemoteWindowConfig$Companion;", "", "()V", "fromCastWindowInfo", "Lcom/example/castapp/model/RemoteWindowConfig;", "windowInfo", "Lcom/example/castapp/model/CastWindowInfo;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.model.RemoteWindowConfig fromCastWindowInfo(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo windowInfo) {
            return null;
        }
    }
}