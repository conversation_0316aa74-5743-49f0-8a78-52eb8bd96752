package com.example.castapp.ui.dialog;

/**
 * 行间距设置对话框
 * 提供行间距管理功能，包括添加、删除、选择行间距
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000t\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\n\u0018\u00002\u00020\u0001:\u000212By\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n0\t\u0012\u0016\b\u0002\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u0012\u0016\b\u0002\u0010\f\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u0012\u0010\b\u0002\u0010\r\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u000e\u00a2\u0006\u0002\u0010\u000fJ\b\u0010$\u001a\u00020\nH\u0002J\u0010\u0010%\u001a\u00020\n2\u0006\u0010&\u001a\u00020\u0005H\u0002J\u0010\u0010\'\u001a\u00020\n2\u0006\u0010(\u001a\u00020)H\u0002J\b\u0010*\u001a\u00020\nH\u0002J\b\u0010+\u001a\u00020\nH\u0002J\u0010\u0010,\u001a\u00020\n2\u0006\u0010&\u001a\u00020\u0005H\u0002J\b\u0010-\u001a\u00020\nH\u0002J\b\u0010.\u001a\u00020\nH\u0002J\u0006\u0010/\u001a\u00020\nJ\b\u00100\u001a\u00020\nH\u0002R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u0019\u001a\u00060\u001aR\u00020\u0000X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001d0\u001cX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\f\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\r\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020#X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00063"}, d2 = {"Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog;", "", "context", "Landroid/content/Context;", "currentLineSpacing", "", "existingLineSpacings", "", "onLineSpacingSelected", "Lkotlin/Function1;", "", "onLineSpacingAdded", "onLineSpacingDeleted", "onResetToDefault", "Lkotlin/Function0;", "(Landroid/content/Context;FLjava/util/List;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V", "btnAddLineSpacing", "Landroid/widget/Button;", "btnClose", "Landroid/widget/ImageView;", "btnResetDefault", "dialog", "Landroid/app/Dialog;", "etNewLineSpacing", "Landroid/widget/EditText;", "lineSpacingAdapter", "Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog$LineSpacingAdapter;", "lineSpacingList", "", "Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog$LineSpacingItem;", "presetLineSpacings", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "selectedLineSpacing", "tvCurrentLineSpacing", "Landroid/widget/TextView;", "addNewLineSpacing", "deleteLineSpacing", "lineSpacing", "initViews", "view", "Landroid/view/View;", "loadLineSpacingList", "resetToDefault", "selectLineSpacing", "setupClickListeners", "setupRecyclerView", "show", "updateCurrentLineSpacingDisplay", "LineSpacingAdapter", "LineSpacingItem", "app_debug"})
public final class LineSpacingSettingsDialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private final float currentLineSpacing = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Float> existingLineSpacings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.Float, kotlin.Unit> onLineSpacingSelected = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<java.lang.Float, kotlin.Unit> onLineSpacingAdded = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<java.lang.Float, kotlin.Unit> onLineSpacingDeleted = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function0<kotlin.Unit> onResetToDefault = null;
    @org.jetbrains.annotations.Nullable()
    private android.app.Dialog dialog;
    private androidx.recyclerview.widget.RecyclerView recyclerView;
    private com.example.castapp.ui.dialog.LineSpacingSettingsDialog.LineSpacingAdapter lineSpacingAdapter;
    private android.widget.ImageView btnClose;
    private android.widget.Button btnAddLineSpacing;
    private android.widget.Button btnResetDefault;
    private android.widget.TextView tvCurrentLineSpacing;
    private android.widget.EditText etNewLineSpacing;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Float> presetLineSpacings = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.ui.dialog.LineSpacingSettingsDialog.LineSpacingItem> lineSpacingList = null;
    private float selectedLineSpacing;
    
    public LineSpacingSettingsDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, float currentLineSpacing, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Float> existingLineSpacings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onLineSpacingSelected, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onLineSpacingAdded, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onLineSpacingDeleted, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onResetToDefault) {
        super();
    }
    
    /**
     * 显示对话框
     */
    public final void show() {
    }
    
    /**
     * 初始化视图
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 设置RecyclerView
     */
    private final void setupRecyclerView() {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 加载行间距列表
     */
    private final void loadLineSpacingList() {
    }
    
    /**
     * 更新当前行间距显示
     */
    private final void updateCurrentLineSpacingDisplay() {
    }
    
    /**
     * 添加新行间距
     */
    private final void addNewLineSpacing() {
    }
    
    /**
     * 选择行间距
     */
    private final void selectLineSpacing(float lineSpacing) {
    }
    
    /**
     * 删除行间距
     */
    private final void deleteLineSpacing(float lineSpacing) {
    }
    
    /**
     * 重置为默认设置
     */
    private final void resetToDefault() {
    }
    
    /**
     * 行间距适配器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0082\u0004\u0018\u00002\u0010\u0012\f\u0012\n0\u0002R\u00060\u0000R\u00020\u00030\u0001:\u0001\u000fB\u0005\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u0005\u001a\u00020\u0006H\u0016J \u0010\u0007\u001a\u00020\b2\u000e\u0010\t\u001a\n0\u0002R\u00060\u0000R\u00020\u00032\u0006\u0010\n\u001a\u00020\u0006H\u0016J \u0010\u000b\u001a\n0\u0002R\u00060\u0000R\u00020\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u0006H\u0016\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog$LineSpacingAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog$LineSpacingAdapter$ViewHolder;", "Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog;", "(Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog;)V", "getItemCount", "", "onBindViewHolder", "", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "ViewHolder", "app_debug"})
    final class LineSpacingAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.example.castapp.ui.dialog.LineSpacingSettingsDialog.LineSpacingAdapter.ViewHolder> {
        
        public LineSpacingAdapter() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.example.castapp.ui.dialog.LineSpacingSettingsDialog.LineSpacingAdapter.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent, int viewType) {
            return null;
        }
        
        @java.lang.Override()
        public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.dialog.LineSpacingSettingsDialog.LineSpacingAdapter.ViewHolder holder, int position) {
        }
        
        @java.lang.Override()
        public int getItemCount() {
            return 0;
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog$LineSpacingAdapter$ViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog$LineSpacingAdapter;Landroid/view/View;)V", "btnDelete", "Landroid/widget/ImageView;", "ivCurrentIndicator", "tvLineSpacing", "Landroid/widget/TextView;", "tvPresetTag", "bind", "", "item", "Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog$LineSpacingItem;", "app_debug"})
        public final class ViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
            @org.jetbrains.annotations.NotNull()
            private final android.widget.TextView tvLineSpacing = null;
            @org.jetbrains.annotations.NotNull()
            private final android.widget.TextView tvPresetTag = null;
            @org.jetbrains.annotations.NotNull()
            private final android.widget.ImageView ivCurrentIndicator = null;
            @org.jetbrains.annotations.NotNull()
            private final android.widget.ImageView btnDelete = null;
            
            public ViewHolder(@org.jetbrains.annotations.NotNull()
            android.view.View itemView) {
                super(null);
            }
            
            public final void bind(@org.jetbrains.annotations.NotNull()
            com.example.castapp.ui.dialog.LineSpacingSettingsDialog.LineSpacingItem item) {
            }
        }
    }
    
    /**
     * 行间距项数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000e\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\'\u0010\u0010\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0011\u001a\u00020\u00052\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\bR\u001a\u0010\u0006\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0006\u0010\b\"\u0004\b\t\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0017"}, d2 = {"Lcom/example/castapp/ui/dialog/LineSpacingSettingsDialog$LineSpacingItem;", "", "lineSpacing", "", "isPreset", "", "isSelected", "(FZZ)V", "()Z", "setSelected", "(Z)V", "getLineSpacing", "()F", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
    public static final class LineSpacingItem {
        private final float lineSpacing = 0.0F;
        private final boolean isPreset = false;
        private boolean isSelected;
        
        public LineSpacingItem(float lineSpacing, boolean isPreset, boolean isSelected) {
            super();
        }
        
        public final float getLineSpacing() {
            return 0.0F;
        }
        
        public final boolean isPreset() {
            return false;
        }
        
        public final boolean isSelected() {
            return false;
        }
        
        public final void setSelected(boolean p0) {
        }
        
        public final float component1() {
            return 0.0F;
        }
        
        public final boolean component2() {
            return false;
        }
        
        public final boolean component3() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.dialog.LineSpacingSettingsDialog.LineSpacingItem copy(float lineSpacing, boolean isPreset, boolean isSelected) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}