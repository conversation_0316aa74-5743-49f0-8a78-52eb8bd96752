package com.example.castapp.ui.adapter;

/**
 * 层级管理器RecyclerView适配器
 * 专门用于层级调整功能，简化显示内容
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u000e\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001:\u0005\"#$%&B\u0005\u00a2\u0006\u0002\u0010\u0004J\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00020\fJ\u0018\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u0011H\u0016J&\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u0006H\u0016J\u0018\u0010\u0014\u001a\u00020\u00032\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0011H\u0016J\u0016\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00112\u0006\u0010\u001b\u001a\u00020\u0011J\u0010\u0010\u001c\u001a\u00020\u000e2\b\u0010\u001d\u001a\u0004\u0018\u00010\bJ\u0010\u0010\u001e\u001a\u00020\u000e2\b\u0010\u001d\u001a\u0004\u0018\u00010\nJ\u0018\u0010\u001f\u001a\u00020\u000e2\u000e\u0010 \u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\fH\u0016J\u0006\u0010!\u001a\u00020\u000eR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"Lcom/example/castapp/ui/adapter/LayerManagerAdapter;", "Landroidx/recyclerview/widget/ListAdapter;", "Lcom/example/castapp/model/CastWindowInfo;", "Lcom/example/castapp/ui/adapter/LayerManagerAdapter$LayerViewHolder;", "()V", "internalList", "", "onItemMoveListener", "Lcom/example/castapp/ui/adapter/LayerManagerAdapter$OnItemMoveListener;", "onNoteChangeListener", "Lcom/example/castapp/ui/adapter/LayerManagerAdapter$OnNoteChangeListener;", "getCurrentWindowList", "", "onBindViewHolder", "", "holder", "position", "", "payloads", "", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "onItemMove", "", "fromPosition", "toPosition", "setOnItemMoveListener", "listener", "setOnNoteChangeListener", "submitList", "list", "updateAllOrderNumbers", "ItemTouchHelperCallback", "LayerViewHolder", "OnItemMoveListener", "OnNoteChangeListener", "WindowDiffCallback", "app_debug"})
public final class LayerManagerAdapter extends androidx.recyclerview.widget.ListAdapter<com.example.castapp.model.CastWindowInfo, com.example.castapp.ui.adapter.LayerManagerAdapter.LayerViewHolder> {
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.LayerManagerAdapter.OnItemMoveListener onItemMoveListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.LayerManagerAdapter.OnNoteChangeListener onNoteChangeListener;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.model.CastWindowInfo> internalList = null;
    
    public LayerManagerAdapter() {
        super(null);
    }
    
    public final void setOnItemMoveListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.LayerManagerAdapter.OnItemMoveListener listener) {
    }
    
    public final void setOnNoteChangeListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.LayerManagerAdapter.OnNoteChangeListener listener) {
    }
    
    @java.lang.Override()
    public void submitList(@org.jetbrains.annotations.Nullable()
    java.util.List<com.example.castapp.model.CastWindowInfo> list) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.CastWindowInfo> getCurrentWindowList() {
        return null;
    }
    
    /**
     * 更新所有序号显示，用于拖拽结束后的丝滑更新
     */
    public final void updateAllOrderNumbers() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.castapp.ui.adapter.LayerManagerAdapter.LayerViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.LayerManagerAdapter.LayerViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.LayerManagerAdapter.LayerViewHolder holder, int position, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Object> payloads) {
    }
    
    /**
     * 处理拖拽移动
     */
    public final boolean onItemMove(int fromPosition, int toPosition) {
        return false;
    }
    
    /**
     * ItemTouchHelper回调，处理拖拽排序
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0006\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0016J\u0018\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0016J\b\u0010\r\u001a\u00020\u000eH\u0016J\b\u0010\u000f\u001a\u00020\u000eH\u0016J \u0010\u0010\u001a\u00020\u000e2\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\nH\u0016J\u0018\u0010\u0012\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\fH\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/ui/adapter/LayerManagerAdapter$ItemTouchHelperCallback;", "Landroidx/recyclerview/widget/ItemTouchHelper$Callback;", "adapter", "Lcom/example/castapp/ui/adapter/LayerManagerAdapter;", "(Lcom/example/castapp/ui/adapter/LayerManagerAdapter;)V", "clearView", "", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "viewHolder", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "getMovementFlags", "", "isItemViewSwipeEnabled", "", "isLongPressDragEnabled", "onMove", "target", "onSwiped", "direction", "app_debug"})
    public static final class ItemTouchHelperCallback extends androidx.recyclerview.widget.ItemTouchHelper.Callback {
        @org.jetbrains.annotations.NotNull()
        private final com.example.castapp.ui.adapter.LayerManagerAdapter adapter = null;
        
        public ItemTouchHelperCallback(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.adapter.LayerManagerAdapter adapter) {
            super();
        }
        
        @java.lang.Override()
        public int getMovementFlags(@org.jetbrains.annotations.NotNull()
        androidx.recyclerview.widget.RecyclerView recyclerView, @org.jetbrains.annotations.NotNull()
        androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder) {
            return 0;
        }
        
        @java.lang.Override()
        public boolean onMove(@org.jetbrains.annotations.NotNull()
        androidx.recyclerview.widget.RecyclerView recyclerView, @org.jetbrains.annotations.NotNull()
        androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder, @org.jetbrains.annotations.NotNull()
        androidx.recyclerview.widget.RecyclerView.ViewHolder target) {
            return false;
        }
        
        @java.lang.Override()
        public void onSwiped(@org.jetbrains.annotations.NotNull()
        androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder, int direction) {
        }
        
        @java.lang.Override()
        public boolean isLongPressDragEnabled() {
            return false;
        }
        
        @java.lang.Override()
        public void clearView(@org.jetbrains.annotations.NotNull()
        androidx.recyclerview.widget.RecyclerView recyclerView, @org.jetbrains.annotations.NotNull()
        androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder) {
        }
        
        @java.lang.Override()
        public boolean isItemViewSwipeEnabled() {
            return false;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J \u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0015J\u001a\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\b\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u0002J\u000e\u0010\u0017\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u0013R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/example/castapp/ui/adapter/LayerManagerAdapter$LayerViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "dragHandle", "Landroid/widget/ImageView;", "getDragHandle", "()Landroid/widget/ImageView;", "tvConnectionId", "Landroid/widget/TextView;", "tvDeviceName", "tvDeviceNote", "tvOrderNumber", "bind", "", "windowInfo", "Lcom/example/castapp/model/CastWindowInfo;", "position", "", "onNoteChangeListener", "Lcom/example/castapp/ui/adapter/LayerManagerAdapter$OnNoteChangeListener;", "showNoteEditDialog", "updateOrderNumber", "app_debug"})
    public static final class LayerViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvOrderNumber = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvDeviceName = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvConnectionId = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvDeviceNote = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView dragHandle = null;
        
        public LayerViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.ImageView getDragHandle() {
            return null;
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo windowInfo, int position, @org.jetbrains.annotations.Nullable()
        com.example.castapp.ui.adapter.LayerManagerAdapter.OnNoteChangeListener onNoteChangeListener) {
        }
        
        /**
         * 只更新序号，用于拖拽结束后的丝滑更新
         */
        public final void updateOrderNumber(int position) {
        }
        
        /**
         * 🏷️ 显示备注编辑对话框
         */
        private final void showNoteEditDialog(com.example.castapp.model.CastWindowInfo windowInfo, com.example.castapp.ui.adapter.LayerManagerAdapter.OnNoteChangeListener onNoteChangeListener) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005H&\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/ui/adapter/LayerManagerAdapter$OnItemMoveListener;", "", "onItemMove", "", "fromPosition", "", "toPosition", "app_debug"})
    public static abstract interface OnItemMoveListener {
        
        public abstract void onItemMove(int fromPosition, int toPosition);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005H&\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/ui/adapter/LayerManagerAdapter$OnNoteChangeListener;", "", "onNoteChanged", "", "connectionId", "", "note", "app_debug"})
    public static abstract interface OnNoteChangeListener {
        
        public abstract void onNoteChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String note);
    }
    
    /**
     * DiffUtil回调，用于高效更新列表
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\u0018\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016J\u0018\u0010\b\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/ui/adapter/LayerManagerAdapter$WindowDiffCallback;", "Landroidx/recyclerview/widget/DiffUtil$ItemCallback;", "Lcom/example/castapp/model/CastWindowInfo;", "()V", "areContentsTheSame", "", "oldItem", "newItem", "areItemsTheSame", "app_debug"})
    static final class WindowDiffCallback extends androidx.recyclerview.widget.DiffUtil.ItemCallback<com.example.castapp.model.CastWindowInfo> {
        
        public WindowDiffCallback() {
            super();
        }
        
        @java.lang.Override()
        public boolean areItemsTheSame(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo oldItem, @org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo newItem) {
            return false;
        }
        
        @java.lang.Override()
        public boolean areContentsTheSame(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo oldItem, @org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo newItem) {
            return false;
        }
    }
}