package com.example.castapp.ui.adapter;

/**
 * 布局详情适配器
 * 用于显示选中布局中包含的窗口详细信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001:\u0002\u000e\u000fB\u0005\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u0016J\u0018\u0010\n\u001a\u00020\u00032\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\tH\u0016\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/ui/adapter/LayoutDetailAdapter;", "Landroidx/recyclerview/widget/ListAdapter;", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "Lcom/example/castapp/ui/adapter/LayoutDetailAdapter$DetailViewHolder;", "()V", "onBindViewHolder", "", "holder", "position", "", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "DetailDiffCallback", "DetailViewHolder", "app_debug"})
public final class LayoutDetailAdapter extends androidx.recyclerview.widget.ListAdapter<com.example.castapp.database.entity.WindowLayoutItemEntity, com.example.castapp.ui.adapter.LayoutDetailAdapter.DetailViewHolder> {
    
    public LayoutDetailAdapter() {
        super(null);
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.castapp.ui.adapter.LayoutDetailAdapter.DetailViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.LayoutDetailAdapter.DetailViewHolder holder, int position) {
    }
    
    /**
     * DiffUtil回调，用于高效更新列表
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\u0018\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016J\u0018\u0010\b\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/ui/adapter/LayoutDetailAdapter$DetailDiffCallback;", "Landroidx/recyclerview/widget/DiffUtil$ItemCallback;", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "()V", "areContentsTheSame", "", "oldItem", "newItem", "areItemsTheSame", "app_debug"})
    static final class DetailDiffCallback extends androidx.recyclerview.widget.DiffUtil.ItemCallback<com.example.castapp.database.entity.WindowLayoutItemEntity> {
        
        public DetailDiffCallback() {
            super();
        }
        
        @java.lang.Override()
        public boolean areItemsTheSame(@org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutItemEntity oldItem, @org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutItemEntity newItem) {
            return false;
        }
        
        @java.lang.Override()
        public boolean areContentsTheSame(@org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutItemEntity oldItem, @org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutItemEntity newItem) {
            return false;
        }
    }
    
    /**
     * 窗口详情ViewHolder
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001dR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001e"}, d2 = {"Lcom/example/castapp/ui/adapter/LayoutDetailAdapter$DetailViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "borderColorText", "Landroid/widget/TextView;", "borderStatusText", "borderWidthText", "connectionIdText", "cornerRadiusText", "cropRectText", "deviceInfoText", "landscapeText", "layerText", "mirrorText", "noteText", "opacityText", "orderNumber", "positionText", "rotationText", "scaleText", "videoPlayStatusText", "visibilityText", "bind", "", "item", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "totalWindows", "", "app_debug"})
    public static final class DetailViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView orderNumber = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView deviceInfoText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView connectionIdText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView positionText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView scaleText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView rotationText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView layerText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView cropRectText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView visibilityText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView landscapeText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView cornerRadiusText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView opacityText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView mirrorText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView borderStatusText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView borderWidthText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView borderColorText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView noteText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView videoPlayStatusText = null;
        
        public DetailViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutItemEntity item, int totalWindows) {
        }
    }
}