package com.example.castapp.ui.adapter;

/**
 * 窗口管理器RecyclerView适配器
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00be\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010 \n\u0002\b\u0013\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001:\"\u0084\u0001\u0085\u0001\u0086\u0001\u0087\u0001\u0088\u0001\u0089\u0001\u008a\u0001\u008b\u0001\u008c\u0001\u008d\u0001\u008e\u0001\u008f\u0001\u0090\u0001\u0091\u0001\u0092\u0001\u0093\u0001\u0094\u0001B\u0005\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010c\u001a\u00020d2\u0006\u0010e\u001a\u00020\u00032\u0006\u0010f\u001a\u00020gH\u0016J&\u0010c\u001a\u00020d2\u0006\u0010e\u001a\u00020\u00032\u0006\u0010f\u001a\u00020g2\f\u0010h\u001a\b\u0012\u0004\u0012\u00020i0\u0006H\u0016J\u0018\u0010j\u001a\u00020\u00032\u0006\u0010k\u001a\u00020l2\u0006\u0010m\u001a\u00020gH\u0016J\u0010\u0010n\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010\nJ\u0010\u0010p\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010\u0010J\u0010\u0010q\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010\u0016J\u0010\u0010r\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010\u001cJ\u0010\u0010s\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010\"J\u0010\u0010t\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010(J\u0010\u0010u\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010.J\u0010\u0010v\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u000104J\u0010\u0010w\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010:J\u0010\u0010x\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010@J\u0010\u0010y\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010FJ\u0010\u0010z\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010LJ\u0010\u0010{\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010RJ\u0010\u0010|\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010XJ\u0010\u0010}\u001a\u00020d2\b\u0010o\u001a\u0004\u0018\u00010^J\u000e\u0010~\u001a\u00020d2\u0006\u0010\u007f\u001a\u00020\bJ\u001b\u0010\u0080\u0001\u001a\u00020d2\u0010\u0010\u0081\u0001\u001a\u000b\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0082\u0001H\u0016J\u0007\u0010\u0083\u0001\u001a\u00020dR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\t\u001a\u0004\u0018\u00010\nX\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010\f\"\u0004\b\r\u0010\u000eR\u001c\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\u0012\"\u0004\b\u0013\u0010\u0014R\u001c\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\u001c\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001d\u0010\u001e\"\u0004\b\u001f\u0010 R\u001c\u0010!\u001a\u0004\u0018\u00010\"X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b#\u0010$\"\u0004\b%\u0010&R\u001c\u0010\'\u001a\u0004\u0018\u00010(X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b)\u0010*\"\u0004\b+\u0010,R\u001c\u0010-\u001a\u0004\u0018\u00010.X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b/\u00100\"\u0004\b1\u00102R\u001c\u00103\u001a\u0004\u0018\u000104X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u00106\"\u0004\b7\u00108R\u001c\u00109\u001a\u0004\u0018\u00010:X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b;\u0010<\"\u0004\b=\u0010>R\u001c\u0010?\u001a\u0004\u0018\u00010@X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bA\u0010B\"\u0004\bC\u0010DR\u001c\u0010E\u001a\u0004\u0018\u00010FX\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bG\u0010H\"\u0004\bI\u0010JR\u001c\u0010K\u001a\u0004\u0018\u00010LX\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bM\u0010N\"\u0004\bO\u0010PR\u001c\u0010Q\u001a\u0004\u0018\u00010RX\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bS\u0010T\"\u0004\bU\u0010VR\u001c\u0010W\u001a\u0004\u0018\u00010XX\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bY\u0010Z\"\u0004\b[\u0010\\R\u001c\u0010]\u001a\u0004\u0018\u00010^X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b_\u0010`\"\u0004\ba\u0010b\u00a8\u0006\u0095\u0001"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter;", "Landroidx/recyclerview/widget/ListAdapter;", "Lcom/example/castapp/model/CastWindowInfo;", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$WindowViewHolder;", "()V", "internalList", "", "isRemoteMode", "", "onAlphaChangeListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnAlphaChangeListener;", "getOnAlphaChangeListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnAlphaChangeListener;", "setOnAlphaChangeListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnAlphaChangeListener;)V", "onBorderColorChangeListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderColorChangeListener;", "getOnBorderColorChangeListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderColorChangeListener;", "setOnBorderColorChangeListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderColorChangeListener;)V", "onBorderSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderSwitchListener;", "getOnBorderSwitchListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderSwitchListener;", "setOnBorderSwitchListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderSwitchListener;)V", "onBorderWidthChangeListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderWidthChangeListener;", "getOnBorderWidthChangeListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderWidthChangeListener;", "setOnBorderWidthChangeListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderWidthChangeListener;)V", "onControlSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnControlSwitchListener;", "getOnControlSwitchListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnControlSwitchListener;", "setOnControlSwitchListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnControlSwitchListener;)V", "onCornerRadiusChangeListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnCornerRadiusChangeListener;", "getOnCornerRadiusChangeListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnCornerRadiusChangeListener;", "setOnCornerRadiusChangeListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnCornerRadiusChangeListener;)V", "onCropSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnCropSwitchListener;", "getOnCropSwitchListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnCropSwitchListener;", "setOnCropSwitchListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnCropSwitchListener;)V", "onEditSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnEditSwitchListener;", "getOnEditSwitchListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnEditSwitchListener;", "setOnEditSwitchListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnEditSwitchListener;)V", "onLandscapeSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnLandscapeSwitchListener;", "getOnLandscapeSwitchListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnLandscapeSwitchListener;", "setOnLandscapeSwitchListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnLandscapeSwitchListener;)V", "onMirrorSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnMirrorSwitchListener;", "getOnMirrorSwitchListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnMirrorSwitchListener;", "setOnMirrorSwitchListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnMirrorSwitchListener;)V", "onNoteChangeListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnNoteChangeListener;", "getOnNoteChangeListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnNoteChangeListener;", "setOnNoteChangeListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnNoteChangeListener;)V", "onTransformSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnTransformSwitchListener;", "getOnTransformSwitchListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnTransformSwitchListener;", "setOnTransformSwitchListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnTransformSwitchListener;)V", "onVideoControlListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnVideoControlListener;", "getOnVideoControlListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnVideoControlListener;", "setOnVideoControlListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnVideoControlListener;)V", "onVisibilitySwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnVisibilitySwitchListener;", "getOnVisibilitySwitchListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnVisibilitySwitchListener;", "setOnVisibilitySwitchListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnVisibilitySwitchListener;)V", "onWindowDeleteListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnWindowDeleteListener;", "getOnWindowDeleteListener$app_debug", "()Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnWindowDeleteListener;", "setOnWindowDeleteListener$app_debug", "(Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnWindowDeleteListener;)V", "onBindViewHolder", "", "holder", "position", "", "payloads", "", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "setOnAlphaChangeListener", "listener", "setOnBorderColorChangeListener", "setOnBorderSwitchListener", "setOnBorderWidthChangeListener", "setOnControlSwitchListener", "setOnCornerRadiusChangeListener", "setOnCropSwitchListener", "setOnEditSwitchListener", "setOnLandscapeSwitchListener", "setOnMirrorSwitchListener", "setOnNoteChangeListener", "setOnTransformSwitchListener", "setOnVideoControlListener", "setOnVisibilitySwitchListener", "setOnWindowDeleteListener", "setRemoteMode", "isRemote", "submitList", "list", "", "updateListenersOnly", "OnAlphaChangeListener", "OnBorderColorChangeListener", "OnBorderSwitchListener", "OnBorderWidthChangeListener", "OnControlSwitchListener", "OnCornerRadiusChangeListener", "OnCropSwitchListener", "OnEditSwitchListener", "OnLandscapeSwitchListener", "OnMirrorSwitchListener", "OnNoteChangeListener", "OnTransformSwitchListener", "OnVideoControlListener", "OnVisibilitySwitchListener", "OnWindowDeleteListener", "WindowDiffCallback", "WindowViewHolder", "app_debug"})
public final class WindowManagerAdapter extends androidx.recyclerview.widget.ListAdapter<com.example.castapp.model.CastWindowInfo, com.example.castapp.ui.adapter.WindowManagerAdapter.WindowViewHolder> {
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnCropSwitchListener onCropSwitchListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnTransformSwitchListener onTransformSwitchListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnVisibilitySwitchListener onVisibilitySwitchListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnMirrorSwitchListener onMirrorSwitchListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnCornerRadiusChangeListener onCornerRadiusChangeListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnAlphaChangeListener onAlphaChangeListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnControlSwitchListener onControlSwitchListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderSwitchListener onBorderSwitchListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderColorChangeListener onBorderColorChangeListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderWidthChangeListener onBorderWidthChangeListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnNoteChangeListener onNoteChangeListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnWindowDeleteListener onWindowDeleteListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnVideoControlListener onVideoControlListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnLandscapeSwitchListener onLandscapeSwitchListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.WindowManagerAdapter.OnEditSwitchListener onEditSwitchListener;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.model.CastWindowInfo> internalList = null;
    private boolean isRemoteMode = false;
    
    public WindowManagerAdapter() {
        super(null);
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnCropSwitchListener getOnCropSwitchListener$app_debug() {
        return null;
    }
    
    public final void setOnCropSwitchListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnCropSwitchListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnTransformSwitchListener getOnTransformSwitchListener$app_debug() {
        return null;
    }
    
    public final void setOnTransformSwitchListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnTransformSwitchListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnVisibilitySwitchListener getOnVisibilitySwitchListener$app_debug() {
        return null;
    }
    
    public final void setOnVisibilitySwitchListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnVisibilitySwitchListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnMirrorSwitchListener getOnMirrorSwitchListener$app_debug() {
        return null;
    }
    
    public final void setOnMirrorSwitchListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnMirrorSwitchListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnCornerRadiusChangeListener getOnCornerRadiusChangeListener$app_debug() {
        return null;
    }
    
    public final void setOnCornerRadiusChangeListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnCornerRadiusChangeListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnAlphaChangeListener getOnAlphaChangeListener$app_debug() {
        return null;
    }
    
    public final void setOnAlphaChangeListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnAlphaChangeListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnControlSwitchListener getOnControlSwitchListener$app_debug() {
        return null;
    }
    
    public final void setOnControlSwitchListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnControlSwitchListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderSwitchListener getOnBorderSwitchListener$app_debug() {
        return null;
    }
    
    public final void setOnBorderSwitchListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderSwitchListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderColorChangeListener getOnBorderColorChangeListener$app_debug() {
        return null;
    }
    
    public final void setOnBorderColorChangeListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderColorChangeListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderWidthChangeListener getOnBorderWidthChangeListener$app_debug() {
        return null;
    }
    
    public final void setOnBorderWidthChangeListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderWidthChangeListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnNoteChangeListener getOnNoteChangeListener$app_debug() {
        return null;
    }
    
    public final void setOnNoteChangeListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnNoteChangeListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnWindowDeleteListener getOnWindowDeleteListener$app_debug() {
        return null;
    }
    
    public final void setOnWindowDeleteListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnWindowDeleteListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnVideoControlListener getOnVideoControlListener$app_debug() {
        return null;
    }
    
    public final void setOnVideoControlListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnVideoControlListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnLandscapeSwitchListener getOnLandscapeSwitchListener$app_debug() {
        return null;
    }
    
    public final void setOnLandscapeSwitchListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnLandscapeSwitchListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.adapter.WindowManagerAdapter.OnEditSwitchListener getOnEditSwitchListener$app_debug() {
        return null;
    }
    
    public final void setOnEditSwitchListener$app_debug(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnEditSwitchListener p0) {
    }
    
    public final void setOnCropSwitchListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnCropSwitchListener listener) {
    }
    
    public final void setOnTransformSwitchListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnTransformSwitchListener listener) {
    }
    
    public final void setOnVisibilitySwitchListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnVisibilitySwitchListener listener) {
    }
    
    public final void setOnMirrorSwitchListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnMirrorSwitchListener listener) {
    }
    
    public final void setOnCornerRadiusChangeListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnCornerRadiusChangeListener listener) {
    }
    
    public final void setOnAlphaChangeListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnAlphaChangeListener listener) {
    }
    
    public final void setOnControlSwitchListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnControlSwitchListener listener) {
    }
    
    public final void setOnBorderSwitchListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderSwitchListener listener) {
    }
    
    public final void setOnBorderColorChangeListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderColorChangeListener listener) {
    }
    
    public final void setOnBorderWidthChangeListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderWidthChangeListener listener) {
    }
    
    public final void setOnNoteChangeListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnNoteChangeListener listener) {
    }
    
    public final void setOnWindowDeleteListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnWindowDeleteListener listener) {
    }
    
    public final void setOnVideoControlListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnVideoControlListener listener) {
    }
    
    public final void setOnLandscapeSwitchListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnLandscapeSwitchListener listener) {
    }
    
    public final void setOnEditSwitchListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.adapter.WindowManagerAdapter.OnEditSwitchListener listener) {
    }
    
    /**
     * 🪟 设置远程模式（只读模式）
     */
    public final void setRemoteMode(boolean isRemote) {
    }
    
    /**
     * 🔧 更新所有ViewHolder的监听器设置（不重置UI状态）
     */
    public final void updateListenersOnly() {
    }
    
    @java.lang.Override()
    public void submitList(@org.jetbrains.annotations.Nullable()
    java.util.List<com.example.castapp.model.CastWindowInfo> list) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.castapp.ui.adapter.WindowManagerAdapter.WindowViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.WindowManagerAdapter.WindowViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.WindowManagerAdapter.WindowViewHolder holder, int position, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Object> payloads) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnAlphaChangeListener;", "", "onAlphaChanged", "", "connectionId", "", "alpha", "", "app_debug"})
    public static abstract interface OnAlphaChangeListener {
        
        public abstract void onAlphaChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, float alpha);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderColorChangeListener;", "", "onBorderColorChanged", "", "connectionId", "", "color", "", "app_debug"})
    public static abstract interface OnBorderColorChangeListener {
        
        public abstract void onBorderColorChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int color);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderSwitchListener;", "", "onBorderSwitchChanged", "", "connectionId", "", "isEnabled", "", "app_debug"})
    public static abstract interface OnBorderSwitchListener {
        
        public abstract void onBorderSwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderWidthChangeListener;", "", "onBorderWidthChanged", "", "connectionId", "", "width", "", "app_debug"})
    public static abstract interface OnBorderWidthChangeListener {
        
        public abstract void onBorderWidthChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, float width);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnControlSwitchListener;", "", "onControlSwitchChanged", "", "connectionId", "", "isEnabled", "", "app_debug"})
    public static abstract interface OnControlSwitchListener {
        
        public abstract void onControlSwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnCornerRadiusChangeListener;", "", "onCornerRadiusChanged", "", "connectionId", "", "cornerRadius", "", "app_debug"})
    public static abstract interface OnCornerRadiusChangeListener {
        
        public abstract void onCornerRadiusChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, float cornerRadius);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnCropSwitchListener;", "", "onCropSwitchChanged", "", "connectionId", "", "isEnabled", "", "app_debug"})
    public static abstract interface OnCropSwitchListener {
        
        public abstract void onCropSwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnEditSwitchListener;", "", "onEditSwitchChanged", "", "connectionId", "", "isEnabled", "", "app_debug"})
    public static abstract interface OnEditSwitchListener {
        
        public abstract void onEditSwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnLandscapeSwitchListener;", "", "onLandscapeSwitchChanged", "", "connectionId", "", "isEnabled", "", "app_debug"})
    public static abstract interface OnLandscapeSwitchListener {
        
        public abstract void onLandscapeSwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnMirrorSwitchListener;", "", "onMirrorSwitchChanged", "", "connectionId", "", "isEnabled", "", "app_debug"})
    public static abstract interface OnMirrorSwitchListener {
        
        public abstract void onMirrorSwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005H&\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnNoteChangeListener;", "", "onNoteChanged", "", "connectionId", "", "note", "app_debug"})
    public static abstract interface OnNoteChangeListener {
        
        public abstract void onNoteChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String note);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\u0018\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\u0018\u0010\t\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\n"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnTransformSwitchListener;", "", "onDragSwitchChanged", "", "connectionId", "", "isEnabled", "", "onRotationSwitchChanged", "onScaleSwitchChanged", "app_debug"})
    public static abstract interface OnTransformSwitchListener {
        
        public abstract void onDragSwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled);
        
        public abstract void onScaleSwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled);
        
        public abstract void onRotationSwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\u0018\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\nH&J\u0018\u0010\u000b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\f\u001a\u00020\u0007H&\u00a8\u0006\r"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnVideoControlListener;", "", "onVideoLoopCountChanged", "", "connectionId", "", "loopCount", "", "onVideoPlaySwitchChanged", "isEnabled", "", "onVideoVolumeChanged", "volume", "app_debug"})
    public static abstract interface OnVideoControlListener {
        
        public abstract void onVideoPlaySwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled);
        
        public abstract void onVideoLoopCountChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int loopCount);
        
        public abstract void onVideoVolumeChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int volume);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnVisibilitySwitchListener;", "", "onVisibilitySwitchChanged", "", "connectionId", "", "isVisible", "", "app_debug"})
    public static abstract interface OnVisibilitySwitchListener {
        
        public abstract void onVisibilitySwitchChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isVisible);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnWindowDeleteListener;", "", "onWindowDelete", "", "windowInfo", "Lcom/example/castapp/model/CastWindowInfo;", "app_debug"})
    public static abstract interface OnWindowDeleteListener {
        
        public abstract void onWindowDelete(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo windowInfo);
    }
    
    /**
     * DiffUtil回调，用于高效更新列表
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\u0018\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016J\u0018\u0010\b\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$WindowDiffCallback;", "Landroidx/recyclerview/widget/DiffUtil$ItemCallback;", "Lcom/example/castapp/model/CastWindowInfo;", "()V", "areContentsTheSame", "", "oldItem", "newItem", "areItemsTheSame", "app_debug"})
    static final class WindowDiffCallback extends androidx.recyclerview.widget.DiffUtil.ItemCallback<com.example.castapp.model.CastWindowInfo> {
        
        public WindowDiffCallback() {
            super();
        }
        
        @java.lang.Override()
        public boolean areItemsTheSame(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo oldItem, @org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo newItem) {
            return false;
        }
        
        @java.lang.Override()
        public boolean areContentsTheSame(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo oldItem, @org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo newItem) {
            return false;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00ae\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020/J\n\u00100\u001a\u0004\u0018\u000101H\u0002J\n\u00102\u001a\u0004\u0018\u000103H\u0002J\n\u00104\u001a\u0004\u0018\u000105H\u0002J\n\u00106\u001a\u0004\u0018\u000107H\u0002J\n\u00108\u001a\u0004\u0018\u000109H\u0002J\n\u0010:\u001a\u0004\u0018\u00010;H\u0002J\"\u0010<\u001a\u00020)2\u0006\u0010*\u001a\u00020+2\u0006\u0010.\u001a\u00020/2\b\u0010=\u001a\u0004\u0018\u00010>H\u0002J\"\u0010?\u001a\u00020)2\u0006\u0010@\u001a\u00020A2\u0006\u0010B\u001a\u00020-2\b\u0010C\u001a\u0004\u0018\u00010DH\u0002J\u001a\u0010E\u001a\u00020)2\u0006\u0010*\u001a\u00020+2\b\u0010F\u001a\u0004\u0018\u00010GH\u0002J0\u0010H\u001a\u00020)2\u0006\u0010I\u001a\u00020J2\u0006\u0010@\u001a\u00020A2\u0006\u0010K\u001a\u00020-2\u0006\u0010L\u001a\u00020M2\u0006\u0010=\u001a\u00020>H\u0002J\u001a\u0010N\u001a\u00020)2\u0006\u0010*\u001a\u00020+2\b\u0010O\u001a\u0004\u0018\u00010PH\u0002J\u0010\u0010Q\u001a\u00020)2\u0006\u0010R\u001a\u00020/H\u0002J\u0010\u0010S\u001a\u00020)2\u0006\u0010R\u001a\u00020/H\u0002J\u0016\u0010T\u001a\u00020)2\u0006\u0010*\u001a\u00020+2\u0006\u0010.\u001a\u00020/J\u000e\u0010U\u001a\u00020)2\u0006\u0010,\u001a\u00020-R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006V"}, d2 = {"Lcom/example/castapp/ui/adapter/WindowManagerAdapter$WindowViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "btnVideoLoopCount", "Landroid/widget/Button;", "ivColorPalette", "Landroid/widget/ImageView;", "ivDeleteWindow", "layoutVideoControls", "seekbarAlpha", "Landroid/widget/SeekBar;", "seekbarBorderWidth", "seekbarCornerRadius", "seekbarVideoVolume", "switchBorder", "Landroidx/appcompat/widget/SwitchCompat;", "switchControl", "switchCrop", "switchDrag", "switchEdit", "switchLandscape", "switchMirror", "switchRotation", "switchScale", "switchVideoPlay", "switchVisible", "tvAlphaValue", "Landroid/widget/TextView;", "tvBorderWidthValue", "tvConnectionId", "tvCornerRadiusValue", "tvDeviceName", "tvDeviceNote", "tvIpAddress", "tvOrderNumber", "tvTransformInfo", "tvVideoVolumeValue", "tvWindowSize", "bind", "", "windowInfo", "Lcom/example/castapp/model/CastWindowInfo;", "position", "", "isRemoteMode", "", "getCurrentBorderSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderSwitchListener;", "getCurrentControlSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnControlSwitchListener;", "getCurrentCropSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnCropSwitchListener;", "getCurrentMirrorSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnMirrorSwitchListener;", "getCurrentTransformSwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnTransformSwitchListener;", "getCurrentVisibilitySwitchListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnVisibilitySwitchListener;", "setupVideoControls", "onVideoControlListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnVideoControlListener;", "showColorPickerDialog", "connectionId", "", "currentBorderColor", "listener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnBorderColorChangeListener;", "showDeleteConfirmDialog", "onWindowDeleteListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnWindowDeleteListener;", "showLoopCountDialog", "context", "Landroid/content/Context;", "currentLoopCount", "prefs", "Landroid/content/SharedPreferences;", "showNoteEditDialog", "onNoteChangeListener", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter$OnNoteChangeListener;", "updateBorderWidthState", "borderEnabled", "updateColorPaletteState", "updateListenersOnly", "updateOrderNumber", "app_debug"})
    public static final class WindowViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvOrderNumber = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvIpAddress = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvWindowSize = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvConnectionId = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvDeviceName = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvDeviceNote = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvTransformInfo = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchCrop = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchDrag = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchScale = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchRotation = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchVisible = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchMirror = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchControl = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchBorder = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchLandscape = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchEdit = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView ivColorPalette = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.SeekBar seekbarBorderWidth = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvBorderWidthValue = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.SeekBar seekbarCornerRadius = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvCornerRadiusValue = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.SeekBar seekbarAlpha = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvAlphaValue = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView ivDeleteWindow = null;
        @org.jetbrains.annotations.NotNull()
        private final android.view.View layoutVideoControls = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.SwitchCompat switchVideoPlay = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnVideoLoopCount = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.SeekBar seekbarVideoVolume = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvVideoVolumeValue = null;
        
        public WindowViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        private final com.example.castapp.ui.adapter.WindowManagerAdapter.OnCropSwitchListener getCurrentCropSwitchListener() {
            return null;
        }
        
        private final com.example.castapp.ui.adapter.WindowManagerAdapter.OnTransformSwitchListener getCurrentTransformSwitchListener() {
            return null;
        }
        
        private final com.example.castapp.ui.adapter.WindowManagerAdapter.OnVisibilitySwitchListener getCurrentVisibilitySwitchListener() {
            return null;
        }
        
        private final com.example.castapp.ui.adapter.WindowManagerAdapter.OnMirrorSwitchListener getCurrentMirrorSwitchListener() {
            return null;
        }
        
        private final com.example.castapp.ui.adapter.WindowManagerAdapter.OnControlSwitchListener getCurrentControlSwitchListener() {
            return null;
        }
        
        private final com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderSwitchListener getCurrentBorderSwitchListener() {
            return null;
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo windowInfo, int position, boolean isRemoteMode) {
        }
        
        /**
         * 🐾 只更新序号，用于拖拽结束后的丝滑更新
         */
        public final void updateOrderNumber(int position) {
        }
        
        /**
         * 🔧 只更新监听器设置，不重置UI状态
         */
        public final void updateListenersOnly(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo windowInfo, boolean isRemoteMode) {
        }
        
        /**
         * 🎨 更新色板图标状态
         */
        private final void updateColorPaletteState(boolean borderEnabled) {
        }
        
        /**
         * 🎯 更新边框宽度滑动条状态
         */
        private final void updateBorderWidthState(boolean borderEnabled) {
        }
        
        /**
         * 显示颜色选择对话框
         */
        private final void showColorPickerDialog(java.lang.String connectionId, int currentBorderColor, com.example.castapp.ui.adapter.WindowManagerAdapter.OnBorderColorChangeListener listener) {
        }
        
        /**
         * 🏷️ 显示备注编辑对话框
         */
        private final void showNoteEditDialog(com.example.castapp.model.CastWindowInfo windowInfo, com.example.castapp.ui.adapter.WindowManagerAdapter.OnNoteChangeListener onNoteChangeListener) {
        }
        
        /**
         * 🗑️ 显示删除确认对话框 - 优化版本
         * 确保对话框在用户点击删除后立即消失，提升用户体验
         */
        private final void showDeleteConfirmDialog(com.example.castapp.model.CastWindowInfo windowInfo, com.example.castapp.ui.adapter.WindowManagerAdapter.OnWindowDeleteListener onWindowDeleteListener) {
        }
        
        /**
         * 🎬 设置视频控制功能
         */
        private final void setupVideoControls(com.example.castapp.model.CastWindowInfo windowInfo, boolean isRemoteMode, com.example.castapp.ui.adapter.WindowManagerAdapter.OnVideoControlListener onVideoControlListener) {
        }
        
        /**
         * 🎬 显示播放次数设置对话框
         */
        private final void showLoopCountDialog(android.content.Context context, java.lang.String connectionId, int currentLoopCount, android.content.SharedPreferences prefs, com.example.castapp.ui.adapter.WindowManagerAdapter.OnVideoControlListener onVideoControlListener) {
        }
    }
}