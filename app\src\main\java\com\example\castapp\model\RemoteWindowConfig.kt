package com.example.castapp.model

import android.graphics.RectF

/**
 * 🎯 统一的窗口参数管理类
 * 用于在遥控端统一管理所有窗口参数，避免分散存储和复杂收集
 */
data class RemoteWindowConfig(
    // 基础信息
    val connectionId: String,
    val ipAddress: String,
    val port: Int,
    val isActive: Boolean,
    val deviceName: String?,
    val note: String?,
    
    // 位置和变换参数（实时更新）
    var positionX: Float,
    var positionY: Float,
    var scaleFactor: Float,
    var rotationAngle: Float,
    val zOrder: Int,
    
    // 功能开关
    val isCropping: Boolean,
    val isDragEnabled: Boolean,
    val isScaleEnabled: Boolean,
    val isRotationEnabled: Boolean,
    var isVisible: Boolean,
    var isMirrored: Boolean,
    
    // 样式参数（实时更新）
    var cornerRadius: Float,
    var alpha: Float,
    var isBorderEnabled: Boolean,
    var borderColor: Int,
    var borderWidth: Float,
    
    // 控制参数
    val isControlEnabled: Boolean,
    val isEditEnabled: Boolean,
    
    // 窗口尺寸
    val baseWindowWidth: Int,
    val baseWindowHeight: Int,
    
    // 背景和模式
    val windowColorEnabled: Boolean,
    val windowBackgroundColor: Int,
    val isLandscapeModeEnabled: Boolean,
    
    // 裁剪参数
    val cropRectRatio: RectF? = null
) {
    
    /**
     * 🔄 从CastWindowInfo创建统一参数
     */
    companion object {
        fun fromCastWindowInfo(windowInfo: CastWindowInfo): RemoteWindowConfig {
            return RemoteWindowConfig(
                connectionId = windowInfo.connectionId,
                ipAddress = windowInfo.ipAddress,
                port = windowInfo.port,
                isActive = windowInfo.isActive,
                deviceName = windowInfo.deviceName,
                note = windowInfo.note,
                positionX = windowInfo.positionX,
                positionY = windowInfo.positionY,
                scaleFactor = windowInfo.scaleFactor,
                rotationAngle = windowInfo.rotationAngle,
                zOrder = windowInfo.zOrder,
                isCropping = windowInfo.isCropping,
                isDragEnabled = windowInfo.isDragEnabled,
                isScaleEnabled = windowInfo.isScaleEnabled,
                isRotationEnabled = windowInfo.isRotationEnabled,
                isVisible = windowInfo.isVisible,
                isMirrored = windowInfo.isMirrored,
                cornerRadius = windowInfo.cornerRadius,
                alpha = windowInfo.alpha,
                isBorderEnabled = windowInfo.isBorderEnabled,
                borderColor = windowInfo.borderColor,
                borderWidth = windowInfo.borderWidth,
                isControlEnabled = windowInfo.isControlEnabled,
                isEditEnabled = windowInfo.isEditEnabled,
                baseWindowWidth = windowInfo.baseWindowWidth,
                baseWindowHeight = windowInfo.baseWindowHeight,
                windowColorEnabled = windowInfo.windowColorEnabled,
                windowBackgroundColor = windowInfo.windowBackgroundColor,
                isLandscapeModeEnabled = windowInfo.isLandscapeModeEnabled,
                cropRectRatio = windowInfo.cropRectRatio
            )
        }
    }
    
    /**
     * 🔄 转换为批量同步消息的数据格式
     */
    fun toBatchSyncData(): Map<String, Any> {
        val data = mutableMapOf<String, Any>(
            "connectionId" to connectionId,
            "ipAddress" to ipAddress,
            "port" to port,
            "isActive" to isActive,
            "deviceName" to (deviceName ?: "未知设备"),
            "note" to (note ?: "无"),
            "positionX" to positionX,
            "positionY" to positionY,
            "scaleFactor" to scaleFactor,
            "rotationAngle" to rotationAngle,
            "zOrder" to zOrder,
            "isCropping" to isCropping,
            "isDragEnabled" to isDragEnabled,
            "isScaleEnabled" to isScaleEnabled,
            "isRotationEnabled" to isRotationEnabled,
            "isVisible" to isVisible,
            "isMirrored" to isMirrored,
            "cornerRadius" to cornerRadius,
            "alpha" to alpha,
            "isControlEnabled" to isControlEnabled,
            "isEditEnabled" to isEditEnabled,
            "isBorderEnabled" to isBorderEnabled,
            "borderColor" to borderColor,
            "borderWidth" to borderWidth,
            "baseWindowWidth" to baseWindowWidth,
            "baseWindowHeight" to baseWindowHeight,
            "windowColorEnabled" to windowColorEnabled,
            "windowBackgroundColor" to windowBackgroundColor,
            "isLandscapeModeEnabled" to isLandscapeModeEnabled
        )
        
        // 添加裁剪区域信息（如果存在）
        cropRectRatio?.let { cropRatio ->
            data["cropRectRatio"] = mapOf(
                "left" to cropRatio.left,
                "top" to cropRatio.top,
                "right" to cropRatio.right,
                "bottom" to cropRatio.bottom
            )
        }
        
        return data
    }
    
    /**
     * 🔄 更新位置参数
     */
    fun updatePosition(x: Float, y: Float) {
        positionX = x
        positionY = y
    }
    
    /**
     * 🔄 更新缩放参数
     */
    fun updateScale(scale: Float) {
        scaleFactor = scale
    }
    
    /**
     * 🔄 更新旋转参数
     */
    fun updateRotation(rotation: Float) {
        rotationAngle = rotation
    }
    
    /**
     * 🔄 更新样式参数
     */
    fun updateStyle(
        cornerRadius: Float? = null,
        alpha: Float? = null,
        isBorderEnabled: Boolean? = null,
        borderColor: Int? = null,
        borderWidth: Float? = null
    ) {
        cornerRadius?.let { this.cornerRadius = it }
        alpha?.let { this.alpha = it }
        isBorderEnabled?.let { this.isBorderEnabled = it }
        borderColor?.let { this.borderColor = it }
        borderWidth?.let { this.borderWidth = it }
    }
    
    /**
     * 🔄 更新可见性
     */
    fun updateVisibility(visible: Boolean) {
        isVisible = visible
    }
    
    /**
     * 🔄 更新镜像状态
     */
    fun updateMirror(mirrored: Boolean) {
        isMirrored = mirrored
    }
}
