package com.example.castapp.model

import android.content.Context
import android.graphics.RectF
import com.example.castapp.utils.AppLog
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.ConcurrentHashMap

/**
 * 🎯 统一的窗口参数管理类
 * 用于在遥控端统一管理所有窗口参数，避免分散存储和复杂收集
 */
data class RemoteWindowConfig(
    // 基础信息
    val connectionId: String,
    val ipAddress: String,
    val port: Int,
    val isActive: Boolean,
    val deviceName: String?,
    val note: String?,

    // 位置和变换参数（实时更新）
    var positionX: Float,
    var positionY: Float,
    var scaleFactor: Float,
    var rotationAngle: Float,
    val zOrder: Int,

    // 功能开关
    val isCropping: Boolean,
    val isDragEnabled: Boolean,
    val isScaleEnabled: Boolean,
    val isRotationEnabled: Boolean,
    var isVisible: Boolean,
    var isMirrored: Boolean,

    // 样式参数（实时更新）
    var cornerRadius: Float,
    var alpha: Float,
    var isBorderEnabled: Boolean,
    var borderColor: Int,
    var borderWidth: Float,

    // 控制参数
    val isControlEnabled: Boolean,
    val isEditEnabled: Boolean,

    // 窗口尺寸
    val baseWindowWidth: Int,
    val baseWindowHeight: Int,

    // 背景和模式
    val windowColorEnabled: Boolean,
    val windowBackgroundColor: Int,
    val isLandscapeModeEnabled: Boolean,

    // 裁剪参数
    val cropRectRatio: RectF? = null,

    // 🎯 新增：数据来源和时间戳
    val dataSource: String = "unknown", // 数据来源：receiver/cache/visualization
    val lastUpdated: Long = System.currentTimeMillis() // 最后更新时间
) {
    
    /**
     * 🔄 从CastWindowInfo创建统一参数
     */
    companion object {
        fun fromCastWindowInfo(windowInfo: CastWindowInfo): RemoteWindowConfig {
            return RemoteWindowConfig(
                connectionId = windowInfo.connectionId,
                ipAddress = windowInfo.ipAddress,
                port = windowInfo.port,
                isActive = windowInfo.isActive,
                deviceName = windowInfo.deviceName,
                note = windowInfo.note,
                positionX = windowInfo.positionX,
                positionY = windowInfo.positionY,
                scaleFactor = windowInfo.scaleFactor,
                rotationAngle = windowInfo.rotationAngle,
                zOrder = windowInfo.zOrder,
                isCropping = windowInfo.isCropping,
                isDragEnabled = windowInfo.isDragEnabled,
                isScaleEnabled = windowInfo.isScaleEnabled,
                isRotationEnabled = windowInfo.isRotationEnabled,
                isVisible = windowInfo.isVisible,
                isMirrored = windowInfo.isMirrored,
                cornerRadius = windowInfo.cornerRadius,
                alpha = windowInfo.alpha,
                isBorderEnabled = windowInfo.isBorderEnabled,
                borderColor = windowInfo.borderColor,
                borderWidth = windowInfo.borderWidth,
                isControlEnabled = windowInfo.isControlEnabled,
                isEditEnabled = windowInfo.isEditEnabled,
                baseWindowWidth = windowInfo.baseWindowWidth,
                baseWindowHeight = windowInfo.baseWindowHeight,
                windowColorEnabled = windowInfo.windowColorEnabled,
                windowBackgroundColor = windowInfo.windowBackgroundColor,
                isLandscapeModeEnabled = windowInfo.isLandscapeModeEnabled,
                cropRectRatio = windowInfo.cropRectRatio
            )
        }
    }
    
    /**
     * 🔄 转换为批量同步消息的数据格式
     */
    fun toBatchSyncData(): Map<String, Any> {
        val data = mutableMapOf<String, Any>(
            "connectionId" to connectionId,
            "ipAddress" to ipAddress,
            "port" to port,
            "isActive" to isActive,
            "deviceName" to (deviceName ?: "未知设备"),
            "note" to (note ?: "无"),
            "positionX" to positionX,
            "positionY" to positionY,
            "scaleFactor" to scaleFactor,
            "rotationAngle" to rotationAngle,
            "zOrder" to zOrder,
            "isCropping" to isCropping,
            "isDragEnabled" to isDragEnabled,
            "isScaleEnabled" to isScaleEnabled,
            "isRotationEnabled" to isRotationEnabled,
            "isVisible" to isVisible,
            "isMirrored" to isMirrored,
            "cornerRadius" to cornerRadius,
            "alpha" to alpha,
            "isControlEnabled" to isControlEnabled,
            "isEditEnabled" to isEditEnabled,
            "isBorderEnabled" to isBorderEnabled,
            "borderColor" to borderColor,
            "borderWidth" to borderWidth,
            "baseWindowWidth" to baseWindowWidth,
            "baseWindowHeight" to baseWindowHeight,
            "windowColorEnabled" to windowColorEnabled,
            "windowBackgroundColor" to windowBackgroundColor,
            "isLandscapeModeEnabled" to isLandscapeModeEnabled
        )
        
        // 添加裁剪区域信息（如果存在）
        cropRectRatio?.let { cropRatio ->
            data["cropRectRatio"] = mapOf(
                "left" to cropRatio.left,
                "top" to cropRatio.top,
                "right" to cropRatio.right,
                "bottom" to cropRatio.bottom
            )
        }
        
        return data
    }
    
    /**
     * 🔄 更新位置参数
     */
    fun updatePosition(x: Float, y: Float) {
        positionX = x
        positionY = y
    }
    
    /**
     * 🔄 更新缩放参数
     */
    fun updateScale(scale: Float) {
        scaleFactor = scale
    }
    
    /**
     * 🔄 更新旋转参数
     */
    fun updateRotation(rotation: Float) {
        rotationAngle = rotation
    }
    
    /**
     * 🔄 更新样式参数
     */
    fun updateStyle(
        cornerRadius: Float? = null,
        alpha: Float? = null,
        isBorderEnabled: Boolean? = null,
        borderColor: Int? = null,
        borderWidth: Float? = null
    ) {
        cornerRadius?.let { this.cornerRadius = it }
        alpha?.let { this.alpha = it }
        isBorderEnabled?.let { this.isBorderEnabled = it }
        borderColor?.let { this.borderColor = it }
        borderWidth?.let { this.borderWidth = it }
    }
    
    /**
     * 🔄 更新可见性
     */
    fun updateVisibility(visible: Boolean) {
        isVisible = visible
    }
    
    /**
     * 🔄 更新镜像状态
     */
    fun updateMirror(mirrored: Boolean) {
        isMirrored = mirrored
    }

    /**
     * 🔄 转换为CastWindowInfo
     */
    fun toCastWindowInfo(): CastWindowInfo {
        return CastWindowInfo(
            connectionId = connectionId,
            ipAddress = ipAddress,
            port = port,
            isActive = isActive,
            deviceName = deviceName,
            note = note,
            positionX = positionX,
            positionY = positionY,
            scaleFactor = scaleFactor,
            rotationAngle = rotationAngle,
            zOrder = zOrder,
            isCropping = isCropping,
            isDragEnabled = isDragEnabled,
            isScaleEnabled = isScaleEnabled,
            isRotationEnabled = isRotationEnabled,
            isVisible = isVisible,
            isMirrored = isMirrored,
            cornerRadius = cornerRadius,
            alpha = alpha,
            isBorderEnabled = isBorderEnabled,
            borderColor = borderColor,
            borderWidth = borderWidth,
            isControlEnabled = isControlEnabled,
            isEditEnabled = isEditEnabled,
            baseWindowWidth = baseWindowWidth,
            baseWindowHeight = baseWindowHeight,
            windowColorEnabled = windowColorEnabled,
            windowBackgroundColor = windowBackgroundColor,
            isLandscapeModeEnabled = isLandscapeModeEnabled,
            cropRectRatio = cropRectRatio
        )
    }
}

/**
 * 🎯 统一的遥控端窗口配置管理器
 * 集中管理所有接收端的窗口参数，避免数据分散和不一致
 */
class RemoteWindowConfigManager private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: RemoteWindowConfigManager? = null

        fun getInstance(): RemoteWindowConfigManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteWindowConfigManager().also { INSTANCE = it }
            }
        }

        private const val PREFS_NAME = "remote_window_config"
        private const val KEY_PREFIX = "config_"
    }

    // 🎯 每个接收端的窗口配置映射：receiverId -> (connectionId -> RemoteWindowConfig)
    private val receiverConfigs = ConcurrentHashMap<String, ConcurrentHashMap<String, RemoteWindowConfig>>()

    // 🎯 状态流，用于通知UI更新
    private val _configStateFlow = MutableStateFlow<Map<String, Map<String, RemoteWindowConfig>>>(emptyMap())
    val configStateFlow: StateFlow<Map<String, Map<String, RemoteWindowConfig>>> = _configStateFlow.asStateFlow()

    private val gson = Gson()

    /**
     * 🎯 从接收端数据更新窗口配置（主要数据源）
     */
    fun updateFromReceiverData(receiverId: String, windowInfoList: List<CastWindowInfo>) {
        AppLog.d("【统一配置管理器】从接收端更新窗口配置: $receiverId, ${windowInfoList.size} 个窗口")

        val receiverConfigMap = receiverConfigs.getOrPut(receiverId) { ConcurrentHashMap() }

        // 清除旧配置
        receiverConfigMap.clear()

        // 添加新配置
        windowInfoList.forEach { windowInfo ->
            val config = RemoteWindowConfig.fromCastWindowInfo(windowInfo).copy(
                dataSource = "receiver",
                lastUpdated = System.currentTimeMillis()
            )
            receiverConfigMap[windowInfo.connectionId] = config
            AppLog.d("【统一配置管理器】更新窗口配置: ${windowInfo.connectionId}")
        }

        // 通知状态更新
        notifyStateChanged()

        AppLog.d("【统一配置管理器】接收端数据更新完成: $receiverId")
    }

    /**
     * 🎯 从可视化组件同步实时参数
     */
    fun syncVisualizationParams(receiverId: String, visualizationDataList: List<com.example.castapp.ui.view.WindowVisualizationData>) {
        AppLog.d("【统一配置管理器】同步可视化参数: $receiverId, ${visualizationDataList.size} 个窗口")

        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        visualizationDataList.forEach { visualData ->
            receiverConfigMap[visualData.connectionId]?.let { config ->
                // 更新实时参数
                config.positionX = visualData.originalX
                config.positionY = visualData.originalY
                config.scaleFactor = visualData.scaleFactor
                config.rotationAngle = visualData.rotationAngle
                config.alpha = visualData.alpha
                config.isVisible = visualData.isVisible
                config.isMirrored = visualData.isMirrored
                config.cornerRadius = visualData.cornerRadius
                config.isBorderEnabled = visualData.isBorderEnabled
                config.borderColor = visualData.borderColor
                config.borderWidth = visualData.borderWidth

                AppLog.d("【统一配置管理器】同步可视化参数: ${visualData.connectionId}")
            }
        }

        // 通知状态更新
        notifyStateChanged()
    }

    /**
     * 🎯 更新单个窗口参数
     */
    fun updateWindowConfig(receiverId: String, connectionId: String, updateAction: (RemoteWindowConfig) -> RemoteWindowConfig) {
        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        receiverConfigMap[connectionId]?.let { currentConfig ->
            val updatedConfig = updateAction(currentConfig).copy(lastUpdated = System.currentTimeMillis())
            receiverConfigMap[connectionId] = updatedConfig

            AppLog.d("【统一配置管理器】更新窗口参数: $receiverId/$connectionId")

            // 通知状态更新
            notifyStateChanged()
        }
    }

    /**
     * 🎯 获取指定接收端的所有窗口配置
     */
    fun getReceiverConfigs(receiverId: String): Map<String, RemoteWindowConfig> {
        return receiverConfigs[receiverId]?.toMap() ?: emptyMap()
    }

    /**
     * 🎯 获取指定窗口配置
     */
    fun getWindowConfig(receiverId: String, connectionId: String): RemoteWindowConfig? {
        return receiverConfigs[receiverId]?.get(connectionId)
    }

    /**
     * 🎯 获取指定接收端的所有窗口信息列表
     */
    fun getWindowInfoList(receiverId: String): List<CastWindowInfo> {
        return receiverConfigs[receiverId]?.values?.map { it.toCastWindowInfo() } ?: emptyList()
    }

    /**
     * 🎯 获取批量同步数据
     */
    fun getBatchSyncData(receiverId: String): List<Map<String, Any>> {
        return receiverConfigs[receiverId]?.values?.map { it.toBatchSyncData() } ?: emptyList()
    }

    /**
     * 🎯 清除指定接收端的配置
     */
    fun clearReceiverConfigs(receiverId: String) {
        receiverConfigs.remove(receiverId)
        notifyStateChanged()
        AppLog.d("【统一配置管理器】已清除接收端配置: $receiverId")
    }

    /**
     * 🎯 保存配置到本地存储
     */
    fun saveToStorage(context: Context, receiverId: String) {
        try {
            val sharedPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val configMap = receiverConfigs[receiverId] ?: return

            val jsonString = gson.toJson(configMap.values.toList())
            sharedPrefs.edit()
                .putString("$KEY_PREFIX$receiverId", jsonString)
                .putLong("${KEY_PREFIX}${receiverId}_timestamp", System.currentTimeMillis())
                .apply()

            AppLog.d("【统一配置管理器】已保存配置到本地存储: $receiverId")
        } catch (e: Exception) {
            AppLog.e("【统一配置管理器】保存配置失败: $receiverId", e)
        }
    }

    /**
     * 🎯 从本地存储加载配置
     */
    fun loadFromStorage(context: Context, receiverId: String): Boolean {
        return try {
            val sharedPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val jsonString = sharedPrefs.getString("$KEY_PREFIX$receiverId", null) ?: return false

            val type = object : TypeToken<List<RemoteWindowConfig>>() {}.type
            val configList: List<RemoteWindowConfig> = gson.fromJson(jsonString, type)

            val receiverConfigMap = ConcurrentHashMap<String, RemoteWindowConfig>()
            configList.forEach { config ->
                receiverConfigMap[config.connectionId] = config
            }

            receiverConfigs[receiverId] = receiverConfigMap
            notifyStateChanged()

            AppLog.d("【统一配置管理器】从本地存储加载配置: $receiverId, ${configList.size} 个窗口")
            true
        } catch (e: Exception) {
            AppLog.e("【统一配置管理器】加载配置失败: $receiverId", e)
            false
        }
    }

    /**
     * 🎯 通知状态变化
     */
    private fun notifyStateChanged() {
        val currentState = receiverConfigs.mapValues { it.value.toMap() }
        _configStateFlow.value = currentState
    }

    /**
     * 🎯 获取配置统计信息
     */
    fun getConfigStats(): String {
        val totalReceivers = receiverConfigs.size
        val totalWindows = receiverConfigs.values.sumOf { it.size }
        return "接收端: $totalReceivers, 窗口: $totalWindows"
    }
}
