package com.example.castapp.ui.adapter;

/**
 * 🎨 自定义色板适配器
 * 用于显示用户保存的自定义颜色
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\b\u0003\u0018\u0000  2\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\u001f B=\u0012\u000e\b\u0002\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007\u0012\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\u0002\u0010\nJ\u000e\u0010\r\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\u0005J\b\u0010\u000f\u001a\u00020\u0005H\u0016J\r\u0010\u0010\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0011J\u0018\u0010\u0012\u001a\u00020\b2\u0006\u0010\u0013\u001a\u00020\u00022\u0006\u0010\u0014\u001a\u00020\u0005H\u0016J\u0018\u0010\u0015\u001a\u00020\u00022\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0005H\u0016J\u000e\u0010\u0019\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\u0005J\u0015\u0010\u001a\u001a\u00020\b2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u001bJ\u0014\u0010\u001c\u001a\u00020\b2\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00050\u001eR\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u000b\u001a\u0004\u0018\u00010\u0005X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\f\u00a8\u0006!"}, d2 = {"Lcom/example/castapp/ui/adapter/CustomColorPaletteAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/example/castapp/ui/adapter/CustomColorPaletteAdapter$ColorViewHolder;", "colors", "", "", "onColorSelected", "Lkotlin/Function1;", "", "onColorLongClick", "(Ljava/util/List;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "selectedColor", "Ljava/lang/Integer;", "addColor", "color", "getItemCount", "getSelectedColor", "()Ljava/lang/Integer;", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "removeColor", "setSelectedColor", "(Ljava/lang/Integer;)V", "updateColors", "newColors", "", "ColorViewHolder", "Companion", "app_debug"})
public final class CustomColorPaletteAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.example.castapp.ui.adapter.CustomColorPaletteAdapter.ColorViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.Integer> colors;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onColorSelected = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onColorLongClick = null;
    private static final int STROKE_WIDTH_DP = 1;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer selectedColor;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.adapter.CustomColorPaletteAdapter.Companion Companion = null;
    
    public CustomColorPaletteAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> colors, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onColorSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onColorLongClick) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.castapp.ui.adapter.CustomColorPaletteAdapter.ColorViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.CustomColorPaletteAdapter.ColorViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    /**
     * 🎨 更新颜色列表
     */
    public final void updateColors(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> newColors) {
    }
    
    /**
     * 🎨 添加颜色
     */
    public final void addColor(int color) {
    }
    
    /**
     * 🎨 删除颜色
     */
    public final void removeColor(int color) {
    }
    
    /**
     * 🎨 设置选中的颜色
     */
    public final void setSelectedColor(@org.jetbrains.annotations.Nullable()
    java.lang.Integer color) {
    }
    
    /**
     * 🎨 获取当前选中的颜色
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getSelectedColor() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/ui/adapter/CustomColorPaletteAdapter$ColorViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "colorView", "getColorView", "()Landroid/view/View;", "app_debug"})
    public static final class ColorViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.view.View colorView = null;
        
        public ColorViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.view.View getColorView() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/castapp/ui/adapter/CustomColorPaletteAdapter$Companion;", "", "()V", "STROKE_WIDTH_DP", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}