package com.example.castapp.ui.dialog;

/**
 * 字体设置对话框
 * 提供字体管理功能，包括添加、删除、选择字体
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000|\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u000e\u0018\u00002\u00020\u0001:\u0002<=B\u0089\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007\u0012\u0016\b\u0002\u0010\n\u001a\u0010\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t\u0018\u00010\u0007\u0012\u0016\b\u0002\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t\u0018\u00010\u0007\u0012\u001c\b\u0002\u0010\f\u001a\u0016\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t\u0018\u00010\r\u0012\u0010\b\u0002\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\u000f\u00a2\u0006\u0002\u0010\u0010J\b\u0010(\u001a\u00020\tH\u0002J\u0010\u0010)\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u001fH\u0002J\u0010\u0010+\u001a\u00020\t2\u0006\u0010,\u001a\u00020#H\u0002J\b\u0010-\u001a\u00020\tH\u0002J\u0010\u0010.\u001a\u00020\t2\u0006\u0010/\u001a\u000200H\u0002J\b\u00101\u001a\u00020\tH\u0002J\u0010\u00102\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u001fH\u0002J\b\u00103\u001a\u00020\tH\u0002J\u0006\u00104\u001a\u00020\tJ\u0010\u00105\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u001fH\u0002J\u0010\u00106\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u001fH\u0002J\b\u00107\u001a\u00020\tH\u0002J\b\u00108\u001a\u00020\tH\u0002J\b\u00109\u001a\u00020\tH\u0002J\u0018\u0010:\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u001f2\u0006\u0010;\u001a\u00020\u0005H\u0002R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082.\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u001b\u001a\u00060\u001cR\u00020\u0000X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001f0\u001eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\n\u001a\u0010\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t\u0018\u00010\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t\u0018\u00010\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\"\u0010\f\u001a\u0016\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t\u0018\u00010\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020!X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\"\u001a\u0004\u0018\u00010#X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010$\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020&X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020&X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006>"}, d2 = {"Lcom/example/castapp/ui/dialog/FontSettingsDialog;", "", "context", "Landroid/content/Context;", "currentFontName", "", "onFontSelected", "Lkotlin/Function1;", "Lcom/example/castapp/utils/FontPresetManager$FontItem;", "", "onFontAdded", "onFontDeleted", "onFontNameUpdated", "Lkotlin/Function2;", "onResetToDefault", "Lkotlin/Function0;", "(Landroid/content/Context;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;)V", "btnAddFont", "Landroid/widget/Button;", "btnClose", "Landroid/widget/ImageView;", "btnResetDefault", "btnSelectFile", "dialog", "Landroidx/appcompat/app/AlertDialog;", "etFontName", "Landroid/widget/EditText;", "fontAdapter", "Lcom/example/castapp/ui/dialog/FontSettingsDialog$FontAdapter;", "fontList", "", "Lcom/example/castapp/ui/dialog/FontSettingsDialog$FontItem;", "rvFonts", "Landroidx/recyclerview/widget/RecyclerView;", "selectedFontFile", "Ljava/io/File;", "selectedFontItem", "tvCurrentFont", "Landroid/widget/TextView;", "tvSelectedFile", "addCustomFont", "deleteFont", "item", "handleSelectedFile", "file", "initData", "initViews", "view", "Landroid/view/View;", "loadFontList", "selectFont", "setupListeners", "show", "showDeleteConfirmDialog", "showEditFontNameDialog", "showFontFilePicker", "showResetConfirmDialog", "updateAddButtonState", "updateFontName", "newName", "FontAdapter", "FontItem", "app_debug"})
public final class FontSettingsDialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String currentFontName = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.castapp.utils.FontPresetManager.FontItem, kotlin.Unit> onFontSelected = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<com.example.castapp.utils.FontPresetManager.FontItem, kotlin.Unit> onFontAdded = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<com.example.castapp.utils.FontPresetManager.FontItem, kotlin.Unit> onFontDeleted = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function2<com.example.castapp.utils.FontPresetManager.FontItem, com.example.castapp.utils.FontPresetManager.FontItem, kotlin.Unit> onFontNameUpdated = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function0<kotlin.Unit> onResetToDefault = null;
    @org.jetbrains.annotations.Nullable()
    private androidx.appcompat.app.AlertDialog dialog;
    private android.widget.TextView tvCurrentFont;
    private androidx.recyclerview.widget.RecyclerView rvFonts;
    private android.widget.EditText etFontName;
    private android.widget.TextView tvSelectedFile;
    private android.widget.Button btnSelectFile;
    private android.widget.Button btnAddFont;
    private android.widget.Button btnResetDefault;
    private android.widget.ImageView btnClose;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.ui.dialog.FontSettingsDialog.FontItem> fontList = null;
    private com.example.castapp.ui.dialog.FontSettingsDialog.FontAdapter fontAdapter;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.utils.FontPresetManager.FontItem selectedFontItem;
    @org.jetbrains.annotations.Nullable()
    private java.io.File selectedFontFile;
    
    public FontSettingsDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String currentFontName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.utils.FontPresetManager.FontItem, kotlin.Unit> onFontSelected, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.example.castapp.utils.FontPresetManager.FontItem, kotlin.Unit> onFontAdded, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.example.castapp.utils.FontPresetManager.FontItem, kotlin.Unit> onFontDeleted, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super com.example.castapp.utils.FontPresetManager.FontItem, ? super com.example.castapp.utils.FontPresetManager.FontItem, kotlin.Unit> onFontNameUpdated, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onResetToDefault) {
        super();
    }
    
    /**
     * 显示对话框
     */
    public final void show() {
    }
    
    /**
     * 初始化视图
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 初始化数据
     */
    private final void initData() {
    }
    
    /**
     * 加载字体列表
     */
    private final void loadFontList() {
    }
    
    /**
     * 设置监听器
     */
    private final void setupListeners() {
    }
    
    /**
     * 显示字体文件选择器
     */
    private final void showFontFilePicker() {
    }
    
    /**
     * 处理选中的文件
     */
    private final void handleSelectedFile(java.io.File file) {
    }
    
    /**
     * 添加自定义字体
     */
    private final void addCustomFont() {
    }
    
    /**
     * 更新添加按钮状态
     */
    private final void updateAddButtonState() {
    }
    
    /**
     * 显示重置确认对话框
     */
    private final void showResetConfirmDialog() {
    }
    
    /**
     * 选择字体
     */
    private final void selectFont(com.example.castapp.ui.dialog.FontSettingsDialog.FontItem item) {
    }
    
    /**
     * 显示字体名称编辑对话框
     */
    private final void showEditFontNameDialog(com.example.castapp.ui.dialog.FontSettingsDialog.FontItem item) {
    }
    
    /**
     * 更新字体名称
     */
    private final void updateFontName(com.example.castapp.ui.dialog.FontSettingsDialog.FontItem item, java.lang.String newName) {
    }
    
    /**
     * 显示删除确认对话框
     */
    private final void showDeleteConfirmDialog(com.example.castapp.ui.dialog.FontSettingsDialog.FontItem item) {
    }
    
    /**
     * 删除字体
     */
    private final void deleteFont(com.example.castapp.ui.dialog.FontSettingsDialog.FontItem item) {
    }
    
    /**
     * 字体适配器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0082\u0004\u0018\u00002\u0010\u0012\f\u0012\n0\u0002R\u00060\u0000R\u00020\u00030\u0001:\u0001\u000fB\u0005\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u0005\u001a\u00020\u0006H\u0016J \u0010\u0007\u001a\u00020\b2\u000e\u0010\t\u001a\n0\u0002R\u00060\u0000R\u00020\u00032\u0006\u0010\n\u001a\u00020\u0006H\u0016J \u0010\u000b\u001a\n0\u0002R\u00060\u0000R\u00020\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u0006H\u0016\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/ui/dialog/FontSettingsDialog$FontAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/example/castapp/ui/dialog/FontSettingsDialog$FontAdapter$FontViewHolder;", "Lcom/example/castapp/ui/dialog/FontSettingsDialog;", "(Lcom/example/castapp/ui/dialog/FontSettingsDialog;)V", "getItemCount", "", "onBindViewHolder", "", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "FontViewHolder", "app_debug"})
    final class FontAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.example.castapp.ui.dialog.FontSettingsDialog.FontAdapter.FontViewHolder> {
        
        public FontAdapter() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.example.castapp.ui.dialog.FontSettingsDialog.FontAdapter.FontViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent, int viewType) {
            return null;
        }
        
        @java.lang.Override()
        public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.dialog.FontSettingsDialog.FontAdapter.FontViewHolder holder, int position) {
        }
        
        @java.lang.Override()
        public int getItemCount() {
            return 0;
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/example/castapp/ui/dialog/FontSettingsDialog$FontAdapter$FontViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/example/castapp/ui/dialog/FontSettingsDialog$FontAdapter;Landroid/view/View;)V", "btnEdit", "Landroid/widget/ImageView;", "ivCurrentIndicator", "tvFontName", "Landroid/widget/TextView;", "tvPresetTag", "bind", "", "item", "Lcom/example/castapp/ui/dialog/FontSettingsDialog$FontItem;", "app_debug"})
        public final class FontViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
            @org.jetbrains.annotations.NotNull()
            private final android.widget.TextView tvFontName = null;
            @org.jetbrains.annotations.NotNull()
            private final android.widget.TextView tvPresetTag = null;
            @org.jetbrains.annotations.NotNull()
            private final android.widget.ImageView ivCurrentIndicator = null;
            @org.jetbrains.annotations.NotNull()
            private final android.widget.ImageView btnEdit = null;
            
            public FontViewHolder(@org.jetbrains.annotations.NotNull()
            android.view.View itemView) {
                super(null);
            }
            
            public final void bind(@org.jetbrains.annotations.NotNull()
            com.example.castapp.ui.dialog.FontSettingsDialog.FontItem item) {
            }
        }
    }
    
    /**
     * 字体显示数据项
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\f\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00052\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0004\u0010\t\"\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"}, d2 = {"Lcom/example/castapp/ui/dialog/FontSettingsDialog$FontItem;", "", "fontItem", "Lcom/example/castapp/utils/FontPresetManager$FontItem;", "isSelected", "", "(Lcom/example/castapp/utils/FontPresetManager$FontItem;Z)V", "getFontItem", "()Lcom/example/castapp/utils/FontPresetManager$FontItem;", "()Z", "setSelected", "(Z)V", "component1", "component2", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
    public static final class FontItem {
        @org.jetbrains.annotations.NotNull()
        private final com.example.castapp.utils.FontPresetManager.FontItem fontItem = null;
        private boolean isSelected;
        
        public FontItem(@org.jetbrains.annotations.NotNull()
        com.example.castapp.utils.FontPresetManager.FontItem fontItem, boolean isSelected) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.FontPresetManager.FontItem getFontItem() {
            return null;
        }
        
        public final boolean isSelected() {
            return false;
        }
        
        public final void setSelected(boolean p0) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.FontPresetManager.FontItem component1() {
            return null;
        }
        
        public final boolean component2() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.dialog.FontSettingsDialog.FontItem copy(@org.jetbrains.annotations.NotNull()
        com.example.castapp.utils.FontPresetManager.FontItem fontItem, boolean isSelected) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}