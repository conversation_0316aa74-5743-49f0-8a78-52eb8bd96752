package com.example.castapp.ui.dialog;

/**
 * 远程接收端控制对话框
 * 用于控制远程接收端设备
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00ca\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u001a\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0002\b\u001c\u0018\u0000 \u0091\u00012\u00020\u0001:\u0002\u0091\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J0\u0010\'\u001a\u00020\u00162\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020\u00152\u0006\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020,2\u0006\u0010.\u001a\u00020/H\u0002J$\u00100\u001a\u00020\u00162\u0006\u0010(\u001a\u00020)2\u0012\u00101\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020302H\u0002J$\u00104\u001a\u00020\u00162\u0006\u0010(\u001a\u00020)2\u0012\u00101\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020302H\u0002J\u0014\u00105\u001a\u000e\u0012\u0004\u0012\u00020/\u0012\u0004\u0012\u00020/06H\u0002J\b\u00107\u001a\u00020\u0016H\u0002J\b\u00108\u001a\u00020\u0016H\u0002J\u001a\u00109\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u000203020\bH\u0002J\"\u0010:\u001a\u00020;2\u0018\u0010<\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u000203020\bH\u0002J\b\u0010=\u001a\u00020>H\u0002J\b\u0010?\u001a\u00020>H\u0002J\b\u0010@\u001a\u00020>H\u0002J\b\u0010A\u001a\u00020\u0016H\u0002J\u0012\u0010B\u001a\u0004\u0018\u00010)2\u0006\u0010C\u001a\u00020\u0015H\u0002J\b\u0010D\u001a\u0004\u0018\u00010&J\b\u0010E\u001a\u00020\u0016H\u0002J\b\u0010F\u001a\u00020\u0016H\u0002J\u0018\u0010G\u001a\u00020\u00162\u0006\u0010C\u001a\u00020\u00152\u0006\u0010H\u001a\u00020,H\u0002J\b\u0010I\u001a\u00020\u0016H\u0002J\b\u0010J\u001a\u00020\u0016H\u0002J\b\u0010K\u001a\u00020\u0016H\u0002J\u000e\u0010L\u001a\u00020\u00162\u0006\u0010M\u001a\u00020\u0015J\u000e\u0010N\u001a\u00020\u00162\u0006\u0010O\u001a\u00020;J\b\u0010P\u001a\u00020\u0016H\u0002J\u000e\u0010Q\u001a\u00020\u00162\u0006\u0010M\u001a\u00020\u0015J\u000e\u0010R\u001a\u00020\u00162\u0006\u0010O\u001a\u00020;J\b\u0010S\u001a\u00020\u0016H\u0002J\b\u0010T\u001a\u00020\u0016H\u0002J\u0016\u0010U\u001a\u00020\u00162\u0006\u0010C\u001a\u00020\u00152\u0006\u0010V\u001a\u00020\tJ\u0010\u0010W\u001a\u00020\u00162\u0006\u0010X\u001a\u00020YH\u0002J\b\u0010Z\u001a\u00020\u0016H\u0002J\b\u0010[\u001a\u00020,H\u0002J\b\u0010\\\u001a\u00020,H\u0002J\u000e\u0010]\u001a\u00020\u00162\u0006\u0010^\u001a\u00020\u0003J\u0012\u0010_\u001a\u00020`2\b\u0010a\u001a\u0004\u0018\u00010bH\u0016J&\u0010c\u001a\u0004\u0018\u00010Y2\u0006\u0010d\u001a\u00020e2\b\u0010f\u001a\u0004\u0018\u00010g2\b\u0010a\u001a\u0004\u0018\u00010bH\u0016J\b\u0010h\u001a\u00020\u0016H\u0016J\b\u0010i\u001a\u00020\u0016H\u0016J\u001a\u0010j\u001a\u00020\u00162\u0006\u0010X\u001a\u00020Y2\b\u0010a\u001a\u0004\u0018\u00010bH\u0016J\b\u0010k\u001a\u00020\u0016H\u0002J\"\u0010l\u001a\u00020\u00162\u0018\u0010m\u001a\u0014\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020,\u0012\u0004\u0012\u00020\u00160\u0014H\u0002J\b\u0010n\u001a\u00020\u0016H\u0002J\b\u0010o\u001a\u00020\u0016H\u0002J\b\u0010p\u001a\u00020\u0016H\u0002J\b\u0010q\u001a\u00020\u0016H\u0002J\b\u0010r\u001a\u00020\u0016H\u0002J\u0010\u0010s\u001a\u00020\u00162\u0006\u0010O\u001a\u00020;H\u0002J\u0018\u0010t\u001a\u00020\u00162\u0006\u0010C\u001a\u00020\u00152\u0006\u0010u\u001a\u00020vH\u0002J \u0010w\u001a\u00020\u00162\u0006\u0010C\u001a\u00020\u00152\u0006\u0010x\u001a\u00020\u00102\u0006\u0010y\u001a\u00020\u0010H\u0002J(\u0010z\u001a\u00020\u00162\u0006\u0010C\u001a\u00020\u00152\u0006\u0010{\u001a\u00020\u00102\u0006\u0010x\u001a\u00020\u00102\u0006\u0010y\u001a\u00020\u0010H\u0002J(\u0010|\u001a\u00020\u00162\u0006\u0010C\u001a\u00020\u00152\u0006\u0010}\u001a\u00020\u00102\u0006\u0010x\u001a\u00020\u00102\u0006\u0010y\u001a\u00020\u0010H\u0002J\b\u0010~\u001a\u00020\u0016H\u0002J\b\u0010\u007f\u001a\u00020\u0016H\u0002J\t\u0010\u0080\u0001\u001a\u00020\u0016H\u0002J\t\u0010\u0081\u0001\u001a\u00020\u0016H\u0002J$\u0010\u0082\u0001\u001a\u00020\u00162\u0019\u0010\u0083\u0001\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u000203020\bH\u0002J\t\u0010\u0084\u0001\u001a\u00020\u0016H\u0002J\u001a\u0010\u0085\u0001\u001a\u00020\u00162\u0006\u0010C\u001a\u00020\u00152\u0007\u0010\u0086\u0001\u001a\u00020,H\u0002J\u001a\u0010\u0087\u0001\u001a\u00020\u00162\u0006\u0010C\u001a\u00020\u00152\u0007\u0010\u0088\u0001\u001a\u00020\u0010H\u0002J$\u0010\u0089\u0001\u001a\u00020\u00162\u0019\u0010\u008a\u0001\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u000203020\bH\u0002J$\u0010\u008b\u0001\u001a\u00020\u00162\u0019\u0010\u0083\u0001\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u000203020\bH\u0002J\u0012\u0010\u008c\u0001\u001a\u00020\u00162\u0007\u0010\u008d\u0001\u001a\u00020,H\u0002J!\u0010\u008e\u0001\u001a\u00020\u00162\r\u0010\u008f\u0001\u001a\b\u0012\u0004\u0012\u00020\t0\b2\t\b\u0002\u0010\u0090\u0001\u001a\u00020,R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R.\u0010\u0013\u001a\u0016\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u0016\u0018\u00010\u0014X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\u000e\u0010\u001b\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020 0\u001fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020&X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0092\u0001"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteReceiverControlDialog;", "Landroidx/fragment/app/DialogFragment;", "remoteReceiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "(Lcom/example/castapp/model/RemoteReceiverConnection;)V", "broadcastButton", "Landroid/widget/Button;", "cachedWindowInfoList", "", "Lcom/example/castapp/model/CastWindowInfo;", "clearScreenButton", "closeButton", "Landroid/widget/ImageButton;", "dialogTitle", "Landroid/widget/TextView;", "dragStartVisualizedX", "", "dragStartVisualizedY", "layerButton", "onWindowParamsUpdated", "Lkotlin/Function2;", "", "", "getOnWindowParamsUpdated", "()Lkotlin/jvm/functions/Function2;", "setOnWindowParamsUpdated", "(Lkotlin/jvm/functions/Function2;)V", "receiveButton", "remoteControlScale", "", "remoteWindowConfig", "", "Lcom/example/castapp/model/RemoteWindowConfig;", "saveButton", "syncButton", "updateButton", "windowButton", "windowVisualizationView", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView;", "applyBasicFormatToRemoteTextWindow", "textWindowView", "Lcom/example/castapp/ui/view/TextWindowView;", "textContent", "isBold", "", "isItalic", "fontSize", "", "applyCompleteFormatDataToRemoteTextWindow", "formatData", "", "", "applyExtendedFormatToRemoteTextWindow", "calculateAdaptiveDialogSize", "Lkotlin/Pair;", "calculateRemoteControlScale", "clearWindowVisualization", "collectAllWindowsParameters", "createBatchSyncMessage", "Lcom/example/castapp/websocket/ControlMessage;", "allWindowsData", "createTemporaryLayerManagerHandler", "Lcom/example/castapp/ui/dialog/RemoteWindowManagerDialog;", "createTemporaryUpdateHandler", "createTemporaryWindowManagerHandler", "executeBatchWindowSync", "findRemoteTextWindowView", "connectionId", "getWindowVisualizationView", "handleBroadcastClick", "handleClearScreenClick", "handleCropModeControl", "isEnabled", "handleLayerClick", "handleReceiveClick", "handleSaveClick", "handleScreenshotError", "errorMessage", "handleScreenshotResponse", "message", "handleSyncClick", "handleTextContentError", "handleTextContentResponse", "handleUpdateClick", "handleWindowClick", "handleWindowParamsUpdate", "updatedWindowInfo", "initViews", "view", "Landroid/view/View;", "initializeUnifiedWindowParams", "isFetchReceiverSettingsEnabled", "isSyncControlEnabled", "onConnectionStateChanged", "newReceiver", "onCreateDialog", "Landroid/app/Dialog;", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroy", "onStart", "onViewCreated", "openLayerManagerDialog", "queryCurrentCropStates", "callback", "registerForConnectionStateUpdates", "requestInitialWindowInfoForVisualization", "requestScreenshotForVisualization", "requestTextContentForVisualization", "requestWindowInfoForVisualization", "sendBatchSyncMessage", "sendWindowCropUpdate", "cropRatio", "Landroid/graphics/RectF;", "sendWindowPositionUpdate", "x", "y", "sendWindowRotationAndPositionUpdate", "rotationAngle", "sendWindowScaleAndPositionUpdate", "scaleFactor", "setupClickListeners", "setupUI", "setupWindowDragListener", "showBatchSyncConfirmationDialog", "syncRemoteTextWindowsWithReceivedData", "textContentsData", "syncVisualizationParams", "updateCachedWindowCropState", "isCropping", "updateLocalVisualizationScaleFactor", "newScaleFactor", "updateScreenshotVisualization", "screenshotsData", "updateTextContentVisualization", "updateUIForConnectionState", "isConnected", "updateWindowVisualization", "windowInfoList", "shouldRequestData", "Companion", "app_debug"})
public final class RemoteReceiverControlDialog extends androidx.fragment.app.DialogFragment {
    @org.jetbrains.annotations.NotNull()
    private com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection;
    private android.widget.TextView dialogTitle;
    private android.widget.ImageButton closeButton;
    private android.widget.Button clearScreenButton;
    private android.widget.Button saveButton;
    private android.widget.Button broadcastButton;
    private android.widget.Button windowButton;
    private android.widget.Button layerButton;
    private android.widget.Button receiveButton;
    private android.widget.Button updateButton;
    private android.widget.Button syncButton;
    private com.example.castapp.ui.view.WindowContainerVisualizationView windowVisualizationView;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.example.castapp.model.CastWindowInfo> cachedWindowInfoList;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.example.castapp.model.RemoteWindowConfig> remoteWindowConfig = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.String, ? super com.example.castapp.model.CastWindowInfo, kotlin.Unit> onWindowParamsUpdated;
    private double remoteControlScale = 1.0;
    private float dragStartVisualizedX = 0.0F;
    private float dragStartVisualizedY = 0.0F;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.dialog.RemoteReceiverControlDialog.Companion Companion = null;
    
    public RemoteReceiverControlDialog(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function2<java.lang.String, com.example.castapp.model.CastWindowInfo, kotlin.Unit> getOnWindowParamsUpdated() {
        return null;
    }
    
    public final void setOnWindowParamsUpdated(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super com.example.castapp.model.CastWindowInfo, kotlin.Unit> p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.app.Dialog onCreateDialog(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public void onStart() {
    }
    
    private final void initViews(android.view.View view) {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void setupUI() {
    }
    
    private final void handleClearScreenClick() {
    }
    
    private final void handleSaveClick() {
    }
    
    private final void handleBroadcastClick() {
    }
    
    private final void handleWindowClick() {
    }
    
    /**
     * 🎯 处理裁剪模式控制
     * @param connectionId 窗口连接ID
     * @param isEnabled 是否启用裁剪模式
     */
    private final void handleCropModeControl(java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 🎯 更新本地可视化数据中的缩放因子
     * 确保下次缩放时基础值正确
     */
    private final void updateLocalVisualizationScaleFactor(java.lang.String connectionId, float newScaleFactor) {
    }
    
    private final void handleReceiveClick() {
    }
    
    private final void handleLayerClick() {
    }
    
    /**
     * 🎯 打开层级管理对话框
     */
    private final void openLayerManagerDialog() {
    }
    
    /**
     * 🎯 创建临时层级管理处理器
     */
    private final com.example.castapp.ui.dialog.RemoteWindowManagerDialog createTemporaryLayerManagerHandler() {
        return null;
    }
    
    private final void handleUpdateClick() {
    }
    
    /**
     * 🎯 初始化统一窗口参数管理器
     */
    private final void initializeUnifiedWindowParams() {
    }
    
    /**
     * 🎯 同步可视化界面的实时参数
     */
    private final void syncVisualizationParams() {
    }
    
    /**
     * 🎯 处理窗口参数更新（从RemoteWindowManagerDialog回调）
     */
    public final void handleWindowParamsUpdate(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.model.CastWindowInfo updatedWindowInfo) {
    }
    
    /**
     * 🔄 处理同步按钮点击事件
     */
    private final void handleSyncClick() {
    }
    
    /**
     * 计算基于接收端屏幕分辨率的自适应对话框尺寸
     * 🚀 优化：基于接收端分辨率等比缩放，保持宽高比
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateAdaptiveDialogSize() {
        return null;
    }
    
    /**
     * 🚀 优化：注册连接状态更新通知（被动监听，零功耗）
     */
    private final void registerForConnectionStateUpdates() {
    }
    
    /**
     * 🚀 被动接收连接状态更新（由父对话框主动调用）
     */
    public final void onConnectionStateChanged(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection newReceiver) {
    }
    
    /**
     * 更新UI以反映连接状态变化
     */
    private final void updateUIForConnectionState(boolean isConnected) {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    /**
     * 计算远程控制窗口的缩放比例
     * 🔧 修复：直接基于实际窗口尺寸计算缩放比例，确保100%一致性
     */
    private final void calculateRemoteControlScale() {
    }
    
    /**
     * 📝 获取窗口可视化视图
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.view.WindowContainerVisualizationView getWindowVisualizationView() {
        return null;
    }
    
    /**
     * 🪟 请求初始窗口信息用于可视化（远程控制窗口创建时调用）
     * 注意：此方法不受"获取接收端设置"开关控制，因为远程控制对话框需要可视化功能
     */
    private final void requestInitialWindowInfoForVisualization() {
    }
    
    /**
     * 🎯 检查"获取接收端设置"开关状态
     */
    private final boolean isFetchReceiverSettingsEnabled() {
        return false;
    }
    
    /**
     * 🪟 请求窗口信息用于可视化（点击窗口按钮时调用）
     */
    private final void requestWindowInfoForVisualization() {
    }
    
    /**
     * 更新窗口容器可视化
     * @param windowInfoList 窗口信息列表
     * @param shouldRequestData 是否应该向接收端请求截图和文字内容数据
     */
    public final void updateWindowVisualization(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList, boolean shouldRequestData) {
    }
    
    /**
     * 🪟 创建临时窗口管理处理器，用于接收初始窗口信息响应
     */
    private final com.example.castapp.ui.dialog.RemoteWindowManagerDialog createTemporaryWindowManagerHandler() {
        return null;
    }
    
    /**
     * 🔄 创建临时更新处理器，用于处理更新按钮的FULL模式窗口信息响应
     */
    private final com.example.castapp.ui.dialog.RemoteWindowManagerDialog createTemporaryUpdateHandler() {
        return null;
    }
    
    /**
     * 📸 请求截图用于可视化显示
     */
    private final void requestScreenshotForVisualization() {
    }
    
    /**
     * 📝 请求文字内容用于可视化显示
     */
    private final void requestTextContentForVisualization() {
    }
    
    /**
     * 📸 处理截图响应消息
     */
    public final void handleScreenshotResponse(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 📸 处理截图错误消息
     */
    public final void handleScreenshotError(@org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage) {
    }
    
    /**
     * 📸 更新截图可视化显示
     */
    private final void updateScreenshotVisualization(java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> screenshotsData) {
    }
    
    /**
     * 📝 更新文字内容可视化显示
     */
    private final void updateTextContentVisualization(java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> textContentsData) {
    }
    
    /**
     * 🎯 新增：同步更新遥控端文字窗口的实际内容和样式
     */
    private final void syncRemoteTextWindowsWithReceivedData(java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> textContentsData) {
    }
    
    /**
     * 🎯 新增：根据connectionId查找遥控端文字窗口
     */
    private final com.example.castapp.ui.view.TextWindowView findRemoteTextWindowView(java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 🎯 新增：将完整格式数据应用到遥控端文字窗口
     */
    private final void applyCompleteFormatDataToRemoteTextWindow(com.example.castapp.ui.view.TextWindowView textWindowView, java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    /**
     * 🎯 新增：应用基本格式到遥控端文字窗口
     */
    private final void applyBasicFormatToRemoteTextWindow(com.example.castapp.ui.view.TextWindowView textWindowView, java.lang.String textContent, boolean isBold, boolean isItalic, int fontSize) {
    }
    
    /**
     * 🎯 新增：应用扩展格式到遥控端文字窗口
     */
    private final void applyExtendedFormatToRemoteTextWindow(com.example.castapp.ui.view.TextWindowView textWindowView, java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    /**
     * 📝 处理文字内容响应消息
     */
    public final void handleTextContentResponse(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 📝 处理文字内容错误消息
     */
    public final void handleTextContentError(@org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage) {
    }
    
    /**
     * 清除窗口容器可视化
     */
    private final void clearWindowVisualization() {
    }
    
    /**
     * 🎯 设置窗口拖动监听器
     */
    private final void setupWindowDragListener() {
    }
    
    /**
     * 🎯 更新本地缓存中指定窗口的裁剪状态
     */
    private final void updateCachedWindowCropState(java.lang.String connectionId, boolean isCropping) {
    }
    
    /**
     * 🎯 新增：查询当前所有窗口的实际裁剪状态
     */
    private final void queryCurrentCropStates(kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 检查同步开关状态
     */
    private final boolean isSyncControlEnabled() {
        return false;
    }
    
    /**
     * 🎯 发送窗口位置更新消息到接收端
     */
    private final void sendWindowPositionUpdate(java.lang.String connectionId, float x, float y) {
    }
    
    /**
     * 🎯 增强型同步：发送窗口缩放和位置组合更新消息到接收端
     */
    private final void sendWindowScaleAndPositionUpdate(java.lang.String connectionId, float scaleFactor, float x, float y) {
    }
    
    /**
     * 🎯 增强型同步：发送窗口旋转和位置组合更新消息到接收端
     */
    private final void sendWindowRotationAndPositionUpdate(java.lang.String connectionId, float rotationAngle, float x, float y) {
    }
    
    /**
     * 🎯 发送窗口裁剪更新消息到接收端
     */
    private final void sendWindowCropUpdate(java.lang.String connectionId, android.graphics.RectF cropRatio) {
    }
    
    /**
     * 🔄 显示批量同步确认对话框
     */
    private final void showBatchSyncConfirmationDialog() {
    }
    
    /**
     * 🔄 执行批量窗口同步
     */
    private final void executeBatchWindowSync() {
    }
    
    /**
     * 🔄 收集所有窗口的完整参数信息
     * 🎯 关键修复：从可视化界面获取实时参数，而不是使用缓存数据
     */
    private final java.util.List<java.util.Map<java.lang.String, java.lang.Object>> collectAllWindowsParameters() {
        return null;
    }
    
    /**
     * 🔄 创建批量同步消息
     */
    private final com.example.castapp.websocket.ControlMessage createBatchSyncMessage(java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> allWindowsData) {
        return null;
    }
    
    /**
     * 🔄 发送批量同步消息到接收端
     */
    private final void sendBatchSyncMessage(com.example.castapp.websocket.ControlMessage message) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteReceiverControlDialog$Companion;", "", "()V", "newInstance", "Lcom/example/castapp/ui/dialog/RemoteReceiverControlDialog;", "receiver", "Lcom/example/castapp/model/RemoteReceiverConnection;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 创建新的远程接收端控制对话框实例
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.dialog.RemoteReceiverControlDialog newInstance(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.RemoteReceiverConnection receiver) {
            return null;
        }
    }
}