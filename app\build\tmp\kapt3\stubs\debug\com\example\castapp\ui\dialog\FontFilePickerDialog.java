package com.example.castapp.ui.dialog;

/**
 * 字体文件选择对话框
 * 提供简单的文件浏览功能，专门用于选择.ttf字体文件
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\u0018\u00002\u00020\u0001:\u0002#$B!\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005\u00a2\u0006\u0002\u0010\bJ\b\u0010\u0018\u001a\u00020\u0006H\u0002J\b\u0010\u0019\u001a\u00020\u001aH\u0002J\u0010\u0010\u001b\u001a\u00020\u00072\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\b\u0010\u001e\u001a\u00020\u0007H\u0002J\b\u0010\u001f\u001a\u00020\u0007H\u0002J\b\u0010 \u001a\u00020\u0007H\u0002J\u0006\u0010!\u001a\u00020\u0007J\b\u0010\"\u001a\u00020\u0007H\u0002R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u000e\u001a\u00060\u000fR\u00020\u0000X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006%"}, d2 = {"Lcom/example/castapp/ui/dialog/FontFilePickerDialog;", "", "context", "Landroid/content/Context;", "onFileSelected", "Lkotlin/Function1;", "Ljava/io/File;", "", "(Landroid/content/Context;Lkotlin/jvm/functions/Function1;)V", "btnClose", "Landroid/widget/ImageView;", "currentDirectory", "dialog", "Landroidx/appcompat/app/AlertDialog;", "fileAdapter", "Lcom/example/castapp/ui/dialog/FontFilePickerDialog$FileAdapter;", "fileList", "", "Lcom/example/castapp/ui/dialog/FontFilePickerDialog$FileItem;", "rvFiles", "Landroidx/recyclerview/widget/RecyclerView;", "tvCurrentPath", "Landroid/widget/TextView;", "tvFileCount", "getInitialDirectory", "hasStoragePermission", "", "initViews", "view", "Landroid/view/View;", "loadCurrentDirectory", "setupListeners", "setupRecyclerView", "show", "showPermissionDialog", "FileAdapter", "FileItem", "app_debug"})
public final class FontFilePickerDialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.io.File, kotlin.Unit> onFileSelected = null;
    @org.jetbrains.annotations.Nullable()
    private androidx.appcompat.app.AlertDialog dialog;
    private android.widget.TextView tvCurrentPath;
    private android.widget.TextView tvFileCount;
    private androidx.recyclerview.widget.RecyclerView rvFiles;
    private android.widget.ImageView btnClose;
    @org.jetbrains.annotations.NotNull()
    private java.io.File currentDirectory;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.ui.dialog.FontFilePickerDialog.FileItem> fileList = null;
    private com.example.castapp.ui.dialog.FontFilePickerDialog.FileAdapter fileAdapter;
    
    public FontFilePickerDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.io.File, kotlin.Unit> onFileSelected) {
        super();
    }
    
    /**
     * 显示对话框
     */
    public final void show() {
    }
    
    /**
     * 检查存储权限
     */
    private final boolean hasStoragePermission() {
        return false;
    }
    
    /**
     * 显示权限请求对话框
     */
    private final void showPermissionDialog() {
    }
    
    /**
     * 初始化视图
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 设置RecyclerView
     */
    private final void setupRecyclerView() {
    }
    
    /**
     * 设置监听器
     */
    private final void setupListeners() {
    }
    
    /**
     * 加载当前目录
     */
    private final void loadCurrentDirectory() {
    }
    
    /**
     * 获取初始目录
     */
    private final java.io.File getInitialDirectory() {
        return null;
    }
    
    /**
     * 文件适配器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0082\u0004\u0018\u00002\u0010\u0012\f\u0012\n0\u0002R\u00060\u0000R\u00020\u00030\u0001:\u0001\u000fB\u0005\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u0005\u001a\u00020\u0006H\u0016J \u0010\u0007\u001a\u00020\b2\u000e\u0010\t\u001a\n0\u0002R\u00060\u0000R\u00020\u00032\u0006\u0010\n\u001a\u00020\u0006H\u0016J \u0010\u000b\u001a\n0\u0002R\u00060\u0000R\u00020\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u0006H\u0016\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/ui/dialog/FontFilePickerDialog$FileAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/example/castapp/ui/dialog/FontFilePickerDialog$FileAdapter$FileViewHolder;", "Lcom/example/castapp/ui/dialog/FontFilePickerDialog;", "(Lcom/example/castapp/ui/dialog/FontFilePickerDialog;)V", "getItemCount", "", "onBindViewHolder", "", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "FileViewHolder", "app_debug"})
    final class FileAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.example.castapp.ui.dialog.FontFilePickerDialog.FileAdapter.FileViewHolder> {
        
        public FileAdapter() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.example.castapp.ui.dialog.FontFilePickerDialog.FileAdapter.FileViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent, int viewType) {
            return null;
        }
        
        @java.lang.Override()
        public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.dialog.FontFilePickerDialog.FileAdapter.FileViewHolder holder, int position) {
        }
        
        @java.lang.Override()
        public int getItemCount() {
            return 0;
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/example/castapp/ui/dialog/FontFilePickerDialog$FileAdapter$FileViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/example/castapp/ui/dialog/FontFilePickerDialog$FileAdapter;Landroid/view/View;)V", "ivIcon", "Landroid/widget/ImageView;", "tvFileName", "Landroid/widget/TextView;", "bind", "", "item", "Lcom/example/castapp/ui/dialog/FontFilePickerDialog$FileItem;", "app_debug"})
        public final class FileViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
            @org.jetbrains.annotations.NotNull()
            private final android.widget.ImageView ivIcon = null;
            @org.jetbrains.annotations.NotNull()
            private final android.widget.TextView tvFileName = null;
            
            public FileViewHolder(@org.jetbrains.annotations.NotNull()
            android.view.View itemView) {
                super(null);
            }
            
            public final void bind(@org.jetbrains.annotations.NotNull()
            com.example.castapp.ui.dialog.FontFilePickerDialog.FileItem item) {
            }
        }
    }
    
    /**
     * 文件项数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000f\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\tJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0007H\u00c6\u0003J1\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00072\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\fR\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0019"}, d2 = {"Lcom/example/castapp/ui/dialog/FontFilePickerDialog$FileItem;", "", "file", "Ljava/io/File;", "name", "", "isDirectory", "", "isTtfFile", "(Ljava/io/File;Ljava/lang/String;ZZ)V", "getFile", "()Ljava/io/File;", "()Z", "getName", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class FileItem {
        @org.jetbrains.annotations.NotNull()
        private final java.io.File file = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String name = null;
        private final boolean isDirectory = false;
        private final boolean isTtfFile = false;
        
        public FileItem(@org.jetbrains.annotations.NotNull()
        java.io.File file, @org.jetbrains.annotations.NotNull()
        java.lang.String name, boolean isDirectory, boolean isTtfFile) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.io.File getFile() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getName() {
            return null;
        }
        
        public final boolean isDirectory() {
            return false;
        }
        
        public final boolean isTtfFile() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.io.File component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        public final boolean component3() {
            return false;
        }
        
        public final boolean component4() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.dialog.FontFilePickerDialog.FileItem copy(@org.jetbrains.annotations.NotNull()
        java.io.File file, @org.jetbrains.annotations.NotNull()
        java.lang.String name, boolean isDirectory, boolean isTtfFile) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}