package com.example.castapp.ui.dialog;

/**
 * 🪟 远程投屏窗口管理BottomSheet对话框
 * 显示远程接收端的所有活跃投屏窗口信息（只读模式）
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00a2\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\u0019\u0018\u0000 o2\u00020\u0001:\u0001oB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u00101\u001a\u00020\u0010H\u0002J\u0010\u00102\u001a\u00020\u00102\u0006\u00103\u001a\u00020\u000fH\u0002J\u0018\u00104\u001a\u00020\u00102\u0006\u00103\u001a\u00020\u000f2\u0006\u00105\u001a\u00020\nH\u0002J\u000e\u00106\u001a\u00020\u00102\u0006\u00107\u001a\u000208J\u0018\u00109\u001a\u00020\u00102\u0006\u00103\u001a\u00020\u000f2\u0006\u0010:\u001a\u00020\nH\u0002J\u0010\u0010;\u001a\u00020\u00102\u0006\u0010<\u001a\u00020=H\u0002J\u0006\u0010\t\u001a\u00020\nJ\b\u0010>\u001a\u00020\u0010H\u0002J&\u0010?\u001a\u0004\u0018\u00010=2\u0006\u0010@\u001a\u00020A2\b\u0010B\u001a\u0004\u0018\u00010C2\b\u0010D\u001a\u0004\u0018\u00010EH\u0016J\u0010\u0010F\u001a\u00020\u00102\u0006\u0010G\u001a\u00020HH\u0016J\u001a\u0010I\u001a\u00020\u00102\u0006\u0010<\u001a\u00020=2\b\u0010D\u001a\u0004\u0018\u00010EH\u0016J\u001a\u0010J\u001a\b\u0012\u0004\u0012\u00020#0\"2\n\u0010K\u001a\u0006\u0012\u0002\b\u00030\"H\u0002J\b\u0010L\u001a\u00020\u0010H\u0002J\b\u0010M\u001a\u00020\u0010H\u0002J\b\u0010N\u001a\u00020\u0010H\u0002J\b\u0010O\u001a\u00020\u0010H\u0002J\u0018\u0010P\u001a\u00020\u00102\u0006\u0010Q\u001a\u00020\u000f2\u0006\u0010R\u001a\u00020\u000fH\u0002J,\u0010S\u001a\u00020\u00102\u0006\u0010Q\u001a\u00020\u000f2\u0006\u0010T\u001a\u00020\u000f2\u0012\u0010U\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020W0VH\u0002J\b\u0010X\u001a\u00020\u0010H\u0002J\b\u0010Y\u001a\u00020\u0010H\u0002J\b\u0010Z\u001a\u00020\u0010H\u0002J\b\u0010[\u001a\u00020\u0010H\u0002J\b\u0010\\\u001a\u00020\u0010H\u0002J\b\u0010]\u001a\u00020\u0010H\u0002J\b\u0010^\u001a\u00020\u0010H\u0002J\u0012\u0010_\u001a\u00020\u00102\b\b\u0002\u00107\u001a\u00020\u000fH\u0002J\u0010\u0010`\u001a\u00020\u00102\u0006\u0010a\u001a\u00020\u000fH\u0002J\b\u0010b\u001a\u00020\u0010H\u0002J\u001c\u0010c\u001a\b\u0012\u0004\u0012\u00020#0\"2\f\u0010d\u001a\b\u0012\u0004\u0012\u00020#0\"H\u0002J\b\u0010e\u001a\u00020\u0010H\u0002J\b\u0010f\u001a\u00020\u0010H\u0002J,\u0010g\u001a\u00020\u00102\u0006\u0010Q\u001a\u00020\u000f2\u0006\u0010T\u001a\u00020\u000f2\u0012\u0010U\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020W0VH\u0002J\u0018\u0010h\u001a\u00020\u00102\u0006\u00103\u001a\u00020\u000f2\u0006\u0010i\u001a\u00020\nH\u0002J\b\u0010j\u001a\u00020\u0010H\u0002J$\u0010k\u001a\u00020\u00102\u0006\u00103\u001a\u00020\u000f2\u0012\u0010l\u001a\u000e\u0012\u0004\u0012\u00020#\u0012\u0004\u0012\u00020#0\u0016H\u0002J\u0016\u0010m\u001a\u00020\u00102\f\u0010n\u001a\b\u0012\u0004\u0012\u00020#0\"H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R.\u0010\r\u001a\u0016\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\u0012\"\u0004\b\u0013\u0010\u0014R:\u0010\u0015\u001a\"\u0012\u0016\u0012\u0014\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00100\u000e\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0016X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\"\u0010\u001b\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u001cX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001d\u0010\u001e\"\u0004\b\u001f\u0010 R.\u0010!\u001a\u0016\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\"\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0016X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010\u0018\"\u0004\b%\u0010\u001aR.\u0010&\u001a\u0016\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020#\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010\u0012\"\u0004\b(\u0010\u0014R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020*X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020,X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020.X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u00100\u001a\b\u0012\u0004\u0012\u00020#0\"X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006p"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteWindowManagerDialog;", "Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;", "remoteReceiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "(Lcom/example/castapp/model/RemoteReceiverConnection;)V", "adapter", "Lcom/example/castapp/ui/adapter/WindowManagerAdapter;", "btnClose", "Landroid/widget/ImageView;", "isSyncEnabled", "", "layoutEmptyState", "Landroid/widget/LinearLayout;", "onCropModeControl", "Lkotlin/Function2;", "", "", "getOnCropModeControl", "()Lkotlin/jvm/functions/Function2;", "setOnCropModeControl", "(Lkotlin/jvm/functions/Function2;)V", "onCropStateQuery", "Lkotlin/Function1;", "getOnCropStateQuery", "()Lkotlin/jvm/functions/Function1;", "setOnCropStateQuery", "(Lkotlin/jvm/functions/Function1;)V", "onDialogDismissed", "Lkotlin/Function0;", "getOnDialogDismissed", "()Lkotlin/jvm/functions/Function0;", "setOnDialogDismissed", "(Lkotlin/jvm/functions/Function0;)V", "onWindowInfoUpdated", "", "Lcom/example/castapp/model/CastWindowInfo;", "getOnWindowInfoUpdated", "setOnWindowInfoUpdated", "onWindowParamsUpdated", "getOnWindowParamsUpdated", "setOnWindowParamsUpdated", "rvCastWindows", "Landroidx/recyclerview/widget/RecyclerView;", "switchSyncControl", "Landroidx/appcompat/widget/SwitchCompat;", "tvWindowCount", "Landroid/widget/TextView;", "tvWindowSettingsTitle", "windowInfoList", "clearWindowOperationListeners", "createRemoteTextWindowForEditing", "connectionId", "handleTextWindowEditModeToggle", "isEnabled", "handleWindowManagerResponse", "message", "Lcom/example/castapp/websocket/ControlMessage;", "hideRemoteTextWindowEditPanelWithSync", "shouldSync", "initViews", "view", "Landroid/view/View;", "loadCachedWindowInfo", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDismiss", "dialog", "Landroid/content/DialogInterface;", "onViewCreated", "parseWindowInfoList", "windowInfoListData", "registerDialog", "requestWindowInfo", "restoreSyncControlState", "saveSyncControlState", "sendNoteUpdate", "targetWindowId", "note", "sendWindowTransformControl", "transformType", "transformData", "", "", "setupClickListeners", "setupCropSwitchListener", "setupEditSwitchListener", "setupLocalOnlyOperationListeners", "setupRecyclerView", "setupSyncControlSwitch", "setupWindowOperationListeners", "showEmptyState", "showErrorState", "errorMessage", "showLoadingState", "syncRealTimeCropState", "cachedWindowList", "unregisterDialog", "updateAdapterSyncMode", "updateLocalVisualizationBorderParams", "updateLocalWindowVisibility", "isVisible", "updateTitle", "updateWindowInfoInCache", "updateAction", "updateWindowList", "windowList", "Companion", "app_debug"})
public final class RemoteWindowManagerDialog extends com.google.android.material.bottomsheet.BottomSheetDialogFragment {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection = null;
    private android.widget.TextView tvWindowSettingsTitle;
    private android.widget.TextView tvWindowCount;
    private androidx.recyclerview.widget.RecyclerView rvCastWindows;
    private android.widget.LinearLayout layoutEmptyState;
    private androidx.appcompat.widget.SwitchCompat switchSyncControl;
    private android.widget.ImageView btnClose;
    private com.example.castapp.ui.adapter.WindowManagerAdapter adapter;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList;
    private boolean isSyncEnabled = false;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onDialogDismissed;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.util.List<com.example.castapp.model.CastWindowInfo>, kotlin.Unit> onWindowInfoUpdated;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit> onCropModeControl;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit>, kotlin.Unit> onCropStateQuery;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.String, ? super com.example.castapp.model.CastWindowInfo, kotlin.Unit> onWindowParamsUpdated;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.dialog.RemoteWindowManagerDialog.Companion Companion = null;
    
    public RemoteWindowManagerDialog(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function0<kotlin.Unit> getOnDialogDismissed() {
        return null;
    }
    
    public final void setOnDialogDismissed(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function1<java.util.List<com.example.castapp.model.CastWindowInfo>, kotlin.Unit> getOnWindowInfoUpdated() {
        return null;
    }
    
    public final void setOnWindowInfoUpdated(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.util.List<com.example.castapp.model.CastWindowInfo>, kotlin.Unit> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function2<java.lang.String, java.lang.Boolean, kotlin.Unit> getOnCropModeControl() {
        return null;
    }
    
    public final void setOnCropModeControl(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function1<kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit>, kotlin.Unit> getOnCropStateQuery() {
        return null;
    }
    
    public final void setOnCropStateQuery(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit>, kotlin.Unit> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function2<java.lang.String, com.example.castapp.model.CastWindowInfo, kotlin.Unit> getOnWindowParamsUpdated() {
        return null;
    }
    
    public final void setOnWindowParamsUpdated(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super com.example.castapp.model.CastWindowInfo, kotlin.Unit> p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 初始化视图组件
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 🔄 恢复保存的实时同步开关状态
     */
    private final void restoreSyncControlState() {
    }
    
    /**
     * 🔄 保存实时同步开关状态
     */
    private final void saveSyncControlState() {
    }
    
    /**
     * 🔄 设置实时同步开关监听器
     */
    private final void setupSyncControlSwitch() {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 🔄 更新适配器同步模式
     */
    private final void updateAdapterSyncMode() {
    }
    
    /**
     * 设置RecyclerView
     */
    private final void setupRecyclerView() {
    }
    
    /**
     * 🎯 设置裁剪开关监听器
     */
    private final void setupCropSwitchListener() {
    }
    
    /**
     * 更新标题显示
     */
    private final void updateTitle() {
    }
    
    /**
     * 🪟 请求远程窗口信息
     */
    private final void requestWindowInfo() {
    }
    
    /**
     * 🪟 处理接收到的窗口信息响应
     */
    public final void handleWindowManagerResponse(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 解析窗口信息列表
     */
    private final java.util.List<com.example.castapp.model.CastWindowInfo> parseWindowInfoList(java.util.List<?> windowInfoListData) {
        return null;
    }
    
    /**
     * 更新窗口列表显示
     */
    private final void updateWindowList(java.util.List<com.example.castapp.model.CastWindowInfo> windowList) {
    }
    
    /**
     * 🎯 加载缓存的窗口信息
     */
    private final void loadCachedWindowInfo() {
    }
    
    /**
     * 🎯 新增：同步实际裁剪状态
     * 检查当前是否有窗口处于裁剪模式，并更新缓存数据中的裁剪状态
     */
    private final java.util.List<com.example.castapp.model.CastWindowInfo> syncRealTimeCropState(java.util.List<com.example.castapp.model.CastWindowInfo> cachedWindowList) {
        return null;
    }
    
    /**
     * 显示空状态
     */
    private final void showEmptyState(java.lang.String message) {
    }
    
    /**
     * 🎯 更新本地缓存中的窗口信息
     */
    private final void updateWindowInfoInCache(java.lang.String connectionId, kotlin.jvm.functions.Function1<? super com.example.castapp.model.CastWindowInfo, com.example.castapp.model.CastWindowInfo> updateAction) {
    }
    
    /**
     * 显示加载状态
     */
    private final void showLoadingState() {
    }
    
    /**
     * 显示错误状态
     */
    private final void showErrorState(java.lang.String errorMessage) {
    }
    
    /**
     * 🔄 设置窗口操作监听器（实时同步开关开启时）
     */
    private final void setupWindowOperationListeners() {
    }
    
    /**
     * 🔄 清除窗口操作监听器（实时同步开关关闭时）
     */
    private final void clearWindowOperationListeners() {
    }
    
    /**
     * 🎯 设置仅本地更新的窗口操作监听器（实时同步开关关闭时）
     */
    private final void setupLocalOnlyOperationListeners() {
    }
    
    /**
     * 🎯 设置编辑开关监听器（独立于实时同步开关）
     */
    private final void setupEditSwitchListener() {
    }
    
    /**
     * 🔄 发送窗口变换控制消息
     */
    private final void sendWindowTransformControl(java.lang.String targetWindowId, java.lang.String transformType, java.util.Map<java.lang.String, ? extends java.lang.Object> transformData) {
    }
    
    /**
     * �️ 发送备注更新消息
     */
    private final void sendNoteUpdate(java.lang.String targetWindowId, java.lang.String note) {
    }
    
    /**
     * �🎯 新增：更新遥控端本地可视化窗口的边框参数
     */
    private final void updateLocalVisualizationBorderParams(java.lang.String targetWindowId, java.lang.String transformType, java.util.Map<java.lang.String, ? extends java.lang.Object> transformData) {
    }
    
    /**
     * 📝 处理文字窗口编辑模式切换
     */
    private final void handleTextWindowEditModeToggle(java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 📝 为编辑创建遥控端文字窗口
     */
    private final void createRemoteTextWindowForEditing(java.lang.String connectionId) {
    }
    
    /**
     * 📝 隐藏遥控端文字窗口编辑面板（带同步控制）
     */
    private final void hideRemoteTextWindowEditPanelWithSync(java.lang.String connectionId, boolean shouldSync) {
    }
    
    /**
     * 📝 获取实时同步开关状态
     */
    public final boolean isSyncEnabled() {
        return false;
    }
    
    /**
     * 🪟 注册对话框到管理器
     */
    private final void registerDialog() {
    }
    
    /**
     * 🪟 注销对话框
     */
    private final void unregisterDialog() {
    }
    
    @java.lang.Override()
    public void onDismiss(@org.jetbrains.annotations.NotNull()
    android.content.DialogInterface dialog) {
    }
    
    /**
     * 🎯 更新遥控端本地可视化窗口的可见性
     */
    private final void updateLocalWindowVisibility(java.lang.String connectionId, boolean isVisible) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteWindowManagerDialog$Companion;", "", "()V", "newInstance", "Lcom/example/castapp/ui/dialog/RemoteWindowManagerDialog;", "remoteReceiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.dialog.RemoteWindowManagerDialog newInstance(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection) {
            return null;
        }
    }
}