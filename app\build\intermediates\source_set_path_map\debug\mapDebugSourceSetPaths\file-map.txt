com.example.castapp-lifecycle-process-2.8.7-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\res
com.example.castapp-lifecycle-livedata-core-ktx-2.8.7-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08d319e430a2dd770ec1a536b952306b\transformed\lifecycle-livedata-core-ktx-2.8.7\res
com.example.castapp-lifecycle-runtime-release-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\436cfa9371b9fe6b6d8dc2735f397ab6\transformed\lifecycle-runtime-release\res
com.example.castapp-cardview-1.0.0-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ea5b3c609f2f0dbad137707758a4070\transformed\cardview-1.0.0\res
com.example.castapp-transition-1.5.0-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b34fcafd738bc0f76beb23b14778aba\transformed\transition-1.5.0\res
com.example.castapp-room-ktx-2.6.1-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5eac572bc5d79469587f41fef3768795\transformed\room-ktx-2.6.1\res
com.example.castapp-emoji2-views-helper-1.3.0-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f1e300ebfcd9a1938350b71ea84b802\transformed\emoji2-views-helper-1.3.0\res
com.example.castapp-activity-ktx-1.9.3-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\742b85b0f667201d1063be02984c899b\transformed\activity-ktx-1.9.3\res
com.example.castapp-viewpager2-1.1.0-beta02-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\754ceb2721ec9435c5544a4d26767ca5\transformed\viewpager2-1.1.0-beta02\res
com.example.castapp-profileinstaller-1.3.1-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\res
com.example.castapp-core-runtime-2.2.0-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78b7e2188878810d09b47a0afd275b87\transformed\core-runtime-2.2.0\res
com.example.castapp-lifecycle-livedata-2.8.7-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78f8605ee6a0193cb2238debf3fc2e72\transformed\lifecycle-livedata-2.8.7\res
com.example.castapp-sqlite-framework-2.4.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7aceb956c4afc46c2d525b16deccb6ca\transformed\sqlite-framework-2.4.0\res
com.example.castapp-lifecycle-viewmodel-savedstate-2.8.7-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8029a7a6240f5dd65441f32c6abd8a5f\transformed\lifecycle-viewmodel-savedstate-2.8.7\res
com.example.castapp-lifecycle-livedata-ktx-2.8.7-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8619a6c361c2475ffca89a7138fc4f5a\transformed\lifecycle-livedata-ktx-2.8.7\res
com.example.castapp-constraintlayout-2.0.1-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c2ebc944a1b713ae7f8e7a60e0d8c64\transformed\constraintlayout-2.0.1\res
com.example.castapp-coordinatorlayout-1.1.0-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8de88fe7432ef8925d03d07e8f25d6f7\transformed\coordinatorlayout-1.1.0\res
com.example.castapp-fragment-ktx-1.8.5-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\919890b08c7d9f2c50942dc12125ca6b\transformed\fragment-ktx-1.8.5\res
com.example.castapp-core-ktx-1.13.1-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\922f6413f75cfdfdca202608fd6f9377\transformed\core-ktx-1.13.1\res
com.example.castapp-fragment-1.8.5-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\966496e81aa0f2b9d3dfaf6862e22ced\transformed\fragment-1.8.5\res
com.example.castapp-savedstate-ktx-1.2.1-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\992746c81f4c49f6db64fe433c4d2b70\transformed\savedstate-ktx-1.2.1\res
com.example.castapp-activity-1.9.3-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab5b74a91933de5b803b79a8692aa797\transformed\activity-1.9.3\res
com.example.castapp-colorpickerview-2.3.0-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aea08ab35834dc872b63d439d8e3e2ec\transformed\colorpickerview-2.3.0\res
com.example.castapp-savedstate-1.2.1-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b16c73ef6056f1a31596023c6be03ebe\transformed\savedstate-1.2.1\res
com.example.castapp-lifecycle-runtime-ktx-release-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39b71c06562d2866e999e733b0dbe9f\transformed\lifecycle-runtime-ktx-release\res
com.example.castapp-recyclerview-1.3.2-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3b921da228be640aba302ba99582620\transformed\recyclerview-1.3.2\res
com.example.castapp-core-1.13.1-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\res
com.example.castapp-room-runtime-2.6.1-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\res
com.example.castapp-appcompat-resources-1.7.0-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d5eaad81b8f7ff11d8d513b9a4ab70\transformed\appcompat-resources-1.7.0\res
com.example.castapp-lifecycle-viewmodel-ktx-2.8.7-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8160253ad106fd84d1b3c72983d659d\transformed\lifecycle-viewmodel-ktx-2.8.7\res
com.example.castapp-customview-poolingcontainer-1.0.0-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbcf9a86d284ab707ef2b95e5f8e5ae3\transformed\customview-poolingcontainer-1.0.0\res
com.example.castapp-emoji2-1.3.0-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\res
com.example.castapp-startup-runtime-1.1.1-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7d7af5fc5f6a597b905327c7d1792a9\transformed\startup-runtime-1.1.1\res
com.example.castapp-annotation-experimental-1.4.0-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db9d38a5b21456e0727dc3e0e5f5d1c2\transformed\annotation-experimental-1.4.0\res
com.example.castapp-material-1.12.0-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbc1af71e9c7a6d81d74e5415e27cca6\transformed\material-1.12.0\res
com.example.castapp-drawerlayout-1.1.1-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91680a5de2e6fdaf18ed75ae680d033\transformed\drawerlayout-1.1.1\res
com.example.castapp-appcompat-1.7.0-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3713a0c8f8ac4a98c195ead755fc68d\transformed\appcompat-1.7.0\res
com.example.castapp-lifecycle-viewmodel-release-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f94c38bb4e4e79fbd3dcaace48e5c418\transformed\lifecycle-viewmodel-release\res
com.example.castapp-sqlite-2.4.0-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcc9f887acf7d3dc8b25bdeef4bd255a\transformed\sqlite-2.4.0\res
com.example.castapp-lifecycle-livedata-core-2.8.7-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3c81bd0e7c2de8c2c20f2eeb9cb9e9\transformed\lifecycle-livedata-core-2.8.7\res
com.example.castapp-resValues-40 D:\Android\AndroidProject\CastAPP\app\build\generated\res\resValues\debug
com.example.castapp-packageDebugResources-41 D:\Android\AndroidProject\CastAPP\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.castapp-packageDebugResources-42 D:\Android\AndroidProject\CastAPP\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.castapp-debug-43 D:\Android\AndroidProject\CastAPP\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.castapp-debug-44 D:\Android\AndroidProject\CastAPP\app\src\debug\res
com.example.castapp-main-45 D:\Android\AndroidProject\CastAPP\app\src\main\res
