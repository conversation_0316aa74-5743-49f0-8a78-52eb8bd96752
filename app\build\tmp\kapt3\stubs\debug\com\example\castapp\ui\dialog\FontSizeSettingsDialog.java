package com.example.castapp.ui.dialog;

/**
 * 字号设置对话框
 * 提供字号管理功能，包括添加、删除、选择字号
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000t\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\b\u0018\u00002\u00020\u0001:\u000234By\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n0\t\u0012\u0016\b\u0002\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u0012\u0016\b\u0002\u0010\f\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u0012\u0010\b\u0002\u0010\r\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u000e\u00a2\u0006\u0002\u0010\u000fJ\b\u0010&\u001a\u00020\nH\u0002J\u0018\u0010\'\u001a\u00020\n2\u0006\u0010(\u001a\u00020\u00052\u0006\u0010)\u001a\u00020\u0005H\u0002J\b\u0010*\u001a\u00020\nH\u0002J\u0010\u0010+\u001a\u00020\n2\u0006\u0010,\u001a\u00020-H\u0002J\b\u0010.\u001a\u00020\nH\u0002J\u0010\u0010/\u001a\u00020\n2\u0006\u0010(\u001a\u00020\u0005H\u0002J\b\u00100\u001a\u00020\nH\u0002J\u0006\u00101\u001a\u00020\nJ\b\u00102\u001a\u00020\nH\u0002R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u0019\u001a\u00060\u001aR\u00020\u0000X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001d0\u001cX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\f\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\r\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\"X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00065"}, d2 = {"Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog;", "", "context", "Landroid/content/Context;", "currentFontSize", "", "existingFontSizes", "", "onFontSizeSelected", "Lkotlin/Function1;", "", "onFontSizeAdded", "onFontSizeDeleted", "onResetToDefault", "Lkotlin/Function0;", "(Landroid/content/Context;ILjava/util/List;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V", "btnAddFontSize", "Landroid/widget/Button;", "btnClose", "Landroid/widget/ImageView;", "btnResetDefault", "dialog", "Landroidx/appcompat/app/AlertDialog;", "etNewFontSize", "Landroid/widget/EditText;", "fontSizeAdapter", "Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog$FontSizeAdapter;", "fontSizeList", "", "Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog$FontSizeItem;", "maxFontSize", "minFontSize", "presetFontSizes", "rvFontSizes", "Landroidx/recyclerview/widget/RecyclerView;", "selectedFontSize", "tvCurrentFontSize", "Landroid/widget/TextView;", "addNewFontSize", "deleteFontSize", "fontSize", "position", "initData", "initViews", "view", "Landroid/view/View;", "resetToDefault", "selectFontSize", "setupListeners", "show", "updateCurrentFontSizeDisplay", "FontSizeAdapter", "FontSizeItem", "app_debug"})
public final class FontSizeSettingsDialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private final int currentFontSize = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Integer> existingFontSizes = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onFontSizeSelected = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onFontSizeAdded = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onFontSizeDeleted = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function0<kotlin.Unit> onResetToDefault = null;
    @org.jetbrains.annotations.Nullable()
    private androidx.appcompat.app.AlertDialog dialog;
    private android.widget.TextView tvCurrentFontSize;
    private androidx.recyclerview.widget.RecyclerView rvFontSizes;
    private android.widget.EditText etNewFontSize;
    private android.widget.Button btnAddFontSize;
    private android.widget.Button btnResetDefault;
    private android.widget.ImageView btnClose;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.ui.dialog.FontSizeSettingsDialog.FontSizeItem> fontSizeList = null;
    private com.example.castapp.ui.dialog.FontSizeSettingsDialog.FontSizeAdapter fontSizeAdapter;
    private int selectedFontSize;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Integer> presetFontSizes = null;
    private final int minFontSize = 4;
    private final int maxFontSize = 90;
    
    public FontSizeSettingsDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, int currentFontSize, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> existingFontSizes, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onFontSizeSelected, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onFontSizeAdded, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onFontSizeDeleted, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onResetToDefault) {
        super();
    }
    
    /**
     * 显示对话框
     */
    public final void show() {
    }
    
    /**
     * 初始化视图
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 初始化数据
     */
    private final void initData() {
    }
    
    /**
     * 设置监听器
     */
    private final void setupListeners() {
    }
    
    /**
     * 添加新字号
     */
    private final void addNewFontSize() {
    }
    
    /**
     * 恢复默认设置
     */
    private final void resetToDefault() {
    }
    
    /**
     * 更新当前字号显示
     */
    private final void updateCurrentFontSizeDisplay() {
    }
    
    /**
     * 选择字号
     */
    private final void selectFontSize(int fontSize) {
    }
    
    /**
     * 删除字号
     */
    private final void deleteFontSize(int fontSize, int position) {
    }
    
    /**
     * 字号列表适配器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0082\u0004\u0018\u00002\u0010\u0012\f\u0012\n0\u0002R\u00060\u0000R\u00020\u00030\u0001:\u0001\u000fB\u0005\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u0005\u001a\u00020\u0006H\u0016J \u0010\u0007\u001a\u00020\b2\u000e\u0010\t\u001a\n0\u0002R\u00060\u0000R\u00020\u00032\u0006\u0010\n\u001a\u00020\u0006H\u0016J \u0010\u000b\u001a\n0\u0002R\u00060\u0000R\u00020\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u0006H\u0016\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog$FontSizeAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog$FontSizeAdapter$FontSizeViewHolder;", "Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog;", "(Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog;)V", "getItemCount", "", "onBindViewHolder", "", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "FontSizeViewHolder", "app_debug"})
    final class FontSizeAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.example.castapp.ui.dialog.FontSizeSettingsDialog.FontSizeAdapter.FontSizeViewHolder> {
        
        public FontSizeAdapter() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.example.castapp.ui.dialog.FontSizeSettingsDialog.FontSizeAdapter.FontSizeViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent, int viewType) {
            return null;
        }
        
        @java.lang.Override()
        public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.dialog.FontSizeSettingsDialog.FontSizeAdapter.FontSizeViewHolder holder, int position) {
        }
        
        @java.lang.Override()
        public int getItemCount() {
            return 0;
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog$FontSizeAdapter$FontSizeViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog$FontSizeAdapter;Landroid/view/View;)V", "btnDelete", "Landroid/widget/ImageView;", "ivCurrentIndicator", "tvFontSize", "Landroid/widget/TextView;", "tvPresetTag", "bind", "", "item", "Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog$FontSizeItem;", "app_debug"})
        public final class FontSizeViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
            @org.jetbrains.annotations.NotNull()
            private final android.widget.TextView tvFontSize = null;
            @org.jetbrains.annotations.NotNull()
            private final android.widget.TextView tvPresetTag = null;
            @org.jetbrains.annotations.NotNull()
            private final android.widget.ImageView ivCurrentIndicator = null;
            @org.jetbrains.annotations.NotNull()
            private final android.widget.ImageView btnDelete = null;
            
            public FontSizeViewHolder(@org.jetbrains.annotations.NotNull()
            android.view.View itemView) {
                super(null);
            }
            
            public final void bind(@org.jetbrains.annotations.NotNull()
            com.example.castapp.ui.dialog.FontSizeSettingsDialog.FontSizeItem item) {
            }
        }
    }
    
    /**
     * 字号数据项
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000f\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\'\u0010\u0010\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0011\u001a\u00020\u00052\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\nR\u001a\u0010\u0006\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0006\u0010\n\"\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"}, d2 = {"Lcom/example/castapp/ui/dialog/FontSizeSettingsDialog$FontSizeItem;", "", "fontSize", "", "isPreset", "", "isSelected", "(IZZ)V", "getFontSize", "()I", "()Z", "setSelected", "(Z)V", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "toString", "", "app_debug"})
    public static final class FontSizeItem {
        private final int fontSize = 0;
        private final boolean isPreset = false;
        private boolean isSelected;
        
        public FontSizeItem(int fontSize, boolean isPreset, boolean isSelected) {
            super();
        }
        
        public final int getFontSize() {
            return 0;
        }
        
        public final boolean isPreset() {
            return false;
        }
        
        public final boolean isSelected() {
            return false;
        }
        
        public final void setSelected(boolean p0) {
        }
        
        public final int component1() {
            return 0;
        }
        
        public final boolean component2() {
            return false;
        }
        
        public final boolean component3() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.dialog.FontSizeSettingsDialog.FontSizeItem copy(int fontSize, boolean isPreset, boolean isSelected) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}