package com.example.castapp.model;

/**
 * 🎯 统一的遥控端窗口配置管理器
 * 集中管理所有接收端的窗口参数，避免数据分散和不一致
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010$\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u0000 \u001b2\u00020\u0001:\u0001\u001bB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J \u0010\t\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u000b0\n2\u0006\u0010\f\u001a\u00020\u0007J\u0016\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\f\u001a\u00020\u0007J\u001c\u0010\u0011\u001a\u00020\u000e2\u0006\u0010\f\u001a\u00020\u00072\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\nJ\u001c\u0010\u0014\u001a\u00020\u000e2\u0006\u0010\f\u001a\u00020\u00072\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\nJ*\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u00072\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b0\u001aR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u0005\u001a\u001a\u0012\u0004\u0012\u00020\u0007\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b0\u00060\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/example/castapp/model/RemoteWindowConfigManager;", "", "()V", "gson", "Lcom/google/gson/Gson;", "receiverConfigs", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/example/castapp/model/RemoteWindowConfig;", "getBatchSyncData", "", "", "receiverId", "saveToStorage", "", "context", "Landroid/content/Context;", "syncVisualizationParams", "visualizationDataList", "Lcom/example/castapp/model/WindowVisualizationData;", "updateFromReceiverData", "windowInfoList", "Lcom/example/castapp/model/CastWindowInfo;", "updateWindowConfig", "connectionId", "updateAction", "Lkotlin/Function1;", "Companion", "app_debug"})
public final class RemoteWindowConfigManager {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.model.RemoteWindowConfigManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREFS_NAME = "remote_window_config";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_PREFIX = "config_";
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.model.RemoteWindowConfig>> receiverConfigs = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.model.RemoteWindowConfigManager.Companion Companion = null;
    
    private RemoteWindowConfigManager() {
        super();
    }
    
    /**
     * 🎯 从接收端数据更新窗口配置（主要数据源）
     */
    public final void updateFromReceiverData(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList) {
    }
    
    /**
     * 🎯 从可视化组件同步实时参数
     */
    public final void syncVisualizationParams(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.WindowVisualizationData> visualizationDataList) {
    }
    
    /**
     * 🎯 更新单个窗口参数
     */
    public final void updateWindowConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteWindowConfig, com.example.castapp.model.RemoteWindowConfig> updateAction) {
    }
    
    /**
     * 🎯 获取批量同步数据
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.util.Map<java.lang.String, java.lang.Object>> getBatchSyncData(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return null;
    }
    
    /**
     * 🎯 保存配置到本地存储
     */
    public final void saveToStorage(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\b\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/model/RemoteWindowConfigManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/model/RemoteWindowConfigManager;", "KEY_PREFIX", "", "PREFS_NAME", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.model.RemoteWindowConfigManager getInstance() {
            return null;
        }
    }
}