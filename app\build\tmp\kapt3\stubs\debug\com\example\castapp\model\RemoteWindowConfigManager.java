package com.example.castapp.model;

/**
 * 🎯 统一的遥控端窗口配置管理器
 * 集中管理所有接收端的窗口参数，避免数据分散和不一致
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u0000 )2\u00020\u0001:\u0001)B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0006J \u0010\u0013\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00050\u00142\u0006\u0010\u0012\u001a\u00020\u0006J\u0006\u0010\u0015\u001a\u00020\u0006J\u001a\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00052\u0006\u0010\u0012\u001a\u00020\u0006J\u0018\u0010\u0017\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u0006J\u0014\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u001a0\u00142\u0006\u0010\u0012\u001a\u00020\u0006J\u0016\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u0012\u001a\u00020\u0006J\b\u0010\u001f\u001a\u00020\u0011H\u0002J\u0016\u0010 \u001a\u00020\u00112\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u0012\u001a\u00020\u0006J\u001c\u0010!\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00062\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020#0\u0014J\u001c\u0010$\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00062\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0014J*\u0010&\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u00062\u0012\u0010\'\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070(R,\u0010\u0003\u001a \u0012\u001c\u0012\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00050\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R/\u0010\b\u001a \u0012\u001c\u0012\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00050\u00050\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u000e\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u000f0\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006*"}, d2 = {"Lcom/example/castapp/model/RemoteWindowConfigManager;", "", "()V", "_configStateFlow", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "", "Lcom/example/castapp/model/RemoteWindowConfig;", "configStateFlow", "Lkotlinx/coroutines/flow/StateFlow;", "getConfigStateFlow", "()Lkotlinx/coroutines/flow/StateFlow;", "gson", "Lcom/google/gson/Gson;", "receiverConfigs", "Ljava/util/concurrent/ConcurrentHashMap;", "clearReceiverConfigs", "", "receiverId", "getBatchSyncData", "", "getConfigStats", "getReceiverConfigs", "getWindowConfig", "connectionId", "getWindowInfoList", "Lcom/example/castapp/model/CastWindowInfo;", "loadFromStorage", "", "context", "Landroid/content/Context;", "notifyStateChanged", "saveToStorage", "syncVisualizationParams", "visualizationDataList", "Lcom/example/castapp/model/WindowVisualizationData;", "updateFromReceiverData", "windowInfoList", "updateWindowConfig", "updateAction", "Lkotlin/Function1;", "Companion", "app_debug"})
public final class RemoteWindowConfigManager {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.model.RemoteWindowConfigManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREFS_NAME = "remote_window_config";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_PREFIX = "config_";
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.model.RemoteWindowConfig>> receiverConfigs = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Map<java.lang.String, java.util.Map<java.lang.String, com.example.castapp.model.RemoteWindowConfig>>> _configStateFlow = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, java.util.Map<java.lang.String, com.example.castapp.model.RemoteWindowConfig>>> configStateFlow = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.model.RemoteWindowConfigManager.Companion Companion = null;
    
    private RemoteWindowConfigManager() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, java.util.Map<java.lang.String, com.example.castapp.model.RemoteWindowConfig>>> getConfigStateFlow() {
        return null;
    }
    
    /**
     * 🎯 从接收端数据更新窗口配置（主要数据源）
     */
    public final void updateFromReceiverData(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList) {
    }
    
    /**
     * 🎯 从可视化组件同步实时参数
     */
    public final void syncVisualizationParams(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.WindowVisualizationData> visualizationDataList) {
    }
    
    /**
     * 🎯 更新单个窗口参数
     */
    public final void updateWindowConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteWindowConfig, com.example.castapp.model.RemoteWindowConfig> updateAction) {
    }
    
    /**
     * 🎯 获取指定接收端的所有窗口配置
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, com.example.castapp.model.RemoteWindowConfig> getReceiverConfigs(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return null;
    }
    
    /**
     * 🎯 获取指定窗口配置
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.model.RemoteWindowConfig getWindowConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 🎯 获取指定接收端的所有窗口信息列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.CastWindowInfo> getWindowInfoList(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return null;
    }
    
    /**
     * 🎯 获取批量同步数据
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.util.Map<java.lang.String, java.lang.Object>> getBatchSyncData(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return null;
    }
    
    /**
     * 🎯 清除指定接收端的配置
     */
    public final void clearReceiverConfigs(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    /**
     * 🎯 保存配置到本地存储
     */
    public final void saveToStorage(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    /**
     * 🎯 从本地存储加载配置
     */
    public final boolean loadFromStorage(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return false;
    }
    
    /**
     * 🎯 通知状态变化
     */
    private final void notifyStateChanged() {
    }
    
    /**
     * 🎯 获取配置统计信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getConfigStats() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\b\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/model/RemoteWindowConfigManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/model/RemoteWindowConfigManager;", "KEY_PREFIX", "", "PREFS_NAME", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.model.RemoteWindowConfigManager getInstance() {
            return null;
        }
    }
}