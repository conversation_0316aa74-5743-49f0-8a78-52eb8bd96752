<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidProject\CastAPP\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidProject\CastAPP\app\src\main\res"><file name="bottom_sheet_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\bottom_sheet_background.xml" qualifiers="" type="drawable"/><file name="button_apply_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\button_apply_background.xml" qualifiers="" type="drawable"/><file name="button_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="button_cancel_apply_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\button_cancel_apply_background.xml" qualifiers="" type="drawable"/><file name="button_cancel_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\button_cancel_background.xml" qualifiers="" type="drawable"/><file name="button_delete_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\button_delete_background.xml" qualifiers="" type="drawable"/><file name="button_primary_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\button_primary_background.xml" qualifiers="" type="drawable"/><file name="button_reset_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\button_reset_background.xml" qualifiers="" type="drawable"/><file name="circle_green" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\circle_green.xml" qualifiers="" type="drawable"/><file name="color_circle_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\color_circle_background.xml" qualifiers="" type="drawable"/><file name="connection_status_indicator" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\connection_status_indicator.xml" qualifiers="" type="drawable"/><file name="count_badge_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\count_badge_background.xml" qualifiers="" type="drawable"/><file name="crop_button_apply_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\crop_button_apply_background.xml" qualifiers="" type="drawable"/><file name="crop_button_cancel_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\crop_button_cancel_background.xml" qualifiers="" type="drawable"/><file name="crop_button_reset_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\crop_button_reset_background.xml" qualifiers="" type="drawable"/><file name="crop_control_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\crop_control_background.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="edittext_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\edittext_background.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="floating_stopwatch_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\floating_stopwatch_background.xml" qualifiers="" type="drawable"/><file name="ic_add" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_add_camera" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_add_camera.xml" qualifiers="" type="drawable"/><file name="ic_add_media" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_add_media.xml" qualifiers="" type="drawable"/><file name="ic_add_picture" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_add_picture.xml" qualifiers="" type="drawable"/><file name="ic_add_text" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_add_text.xml" qualifiers="" type="drawable"/><file name="ic_add_video" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_add_video.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_cast" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_cast.xml" qualifiers="" type="drawable"/><file name="ic_check" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_clear" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_clear.xml" qualifiers="" type="drawable"/><file name="ic_close" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_director" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_director.xml" qualifiers="" type="drawable"/><file name="ic_drag_handle" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_drag_handle.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_error" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_error.xml" qualifiers="" type="drawable"/><file name="ic_file" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_file.xml" qualifiers="" type="drawable"/><file name="ic_folder" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_folder.xml" qualifiers="" type="drawable"/><file name="ic_font" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_font.xml" qualifiers="" type="drawable"/><file name="ic_format_align_center" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_format_align_center.xml" qualifiers="" type="drawable"/><file name="ic_format_align_left" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_format_align_left.xml" qualifiers="" type="drawable"/><file name="ic_format_align_right" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_format_align_right.xml" qualifiers="" type="drawable"/><file name="ic_format_bold" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_format_bold.xml" qualifiers="" type="drawable"/><file name="ic_format_clear" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_format_clear.xml" qualifiers="" type="drawable"/><file name="ic_format_italic" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_format_italic.xml" qualifiers="" type="drawable"/><file name="ic_info" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_layer" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_layer.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_palette" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_palette.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_remote_control" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_remote_control.xml" qualifiers="" type="drawable"/><file name="ic_save" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_save.xml" qualifiers="" type="drawable"/><file name="ic_send" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_send.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_stopwatch" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_stopwatch.xml" qualifiers="" type="drawable"/><file name="ic_updated" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_updated.xml" qualifiers="" type="drawable"/><file name="ic_window_settings" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\ic_window_settings.xml" qualifiers="" type="drawable"/><file name="info_card_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\info_card_background.xml" qualifiers="" type="drawable"/><file name="item_applied_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\item_applied_background.xml" qualifiers="" type="drawable"/><file name="item_normal_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\item_normal_background.xml" qualifiers="" type="drawable"/><file name="item_selected_applied_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\item_selected_applied_background.xml" qualifiers="" type="drawable"/><file name="item_selected_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\item_selected_background.xml" qualifiers="" type="drawable"/><file name="list_item_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\list_item_background.xml" qualifiers="" type="drawable"/><file name="palette" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\palette.xml" qualifiers="" type="drawable"/><file name="precision_control_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\precision_control_background.xml" qualifiers="" type="drawable"/><file name="rounded_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\rounded_background.xml" qualifiers="" type="drawable"/><file name="spinner_background" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="wheel" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\drawable\wheel.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="crop_control_buttons" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\crop_control_buttons.xml" qualifiers="" type="layout"/><file name="dialog_add_media" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_add_media.xml" qualifiers="" type="layout"/><file name="dialog_add_receiver" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_add_receiver.xml" qualifiers="" type="layout"/><file name="dialog_add_remote_device" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_add_remote_device.xml" qualifiers="" type="layout"/><file name="dialog_color_picker" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_color_picker.xml" qualifiers="" type="layout"/><file name="dialog_director" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_director.xml" qualifiers="" type="layout"/><file name="dialog_edit_layout" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_edit_layout.xml" qualifiers="" type="layout"/><file name="dialog_font_file_picker" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_font_file_picker.xml" qualifiers="" type="layout"/><file name="dialog_font_settings" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_font_settings.xml" qualifiers="" type="layout"/><file name="dialog_font_size_settings" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_font_size_settings.xml" qualifiers="" type="layout"/><file name="dialog_layer_manager" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_layer_manager.xml" qualifiers="" type="layout"/><file name="dialog_letter_spacing_settings" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_letter_spacing_settings.xml" qualifiers="" type="layout"/><file name="dialog_line_spacing_settings" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_line_spacing_settings.xml" qualifiers="" type="layout"/><file name="dialog_note_edit" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_note_edit.xml" qualifiers="" type="layout"/><file name="dialog_receive" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_receive.xml" qualifiers="" type="layout"/><file name="dialog_remote_control_manager" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_remote_control_manager.xml" qualifiers="" type="layout"/><file name="dialog_remote_layer_manager" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_remote_layer_manager.xml" qualifiers="" type="layout"/><file name="dialog_remote_receiver_control" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_remote_receiver_control.xml" qualifiers="" type="layout"/><file name="dialog_remote_receiver_settings_control" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_remote_receiver_settings_control.xml" qualifiers="" type="layout"/><file name="dialog_remote_sender_control" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_remote_sender_control.xml" qualifiers="" type="layout"/><file name="dialog_save_director_layout" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_save_director_layout.xml" qualifiers="" type="layout"/><file name="dialog_save_options" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_save_options.xml" qualifiers="" type="layout"/><file name="dialog_send" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_send.xml" qualifiers="" type="layout"/><file name="dialog_window_settings" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\dialog_window_settings.xml" qualifiers="" type="layout"/><file name="floating_stopwatch_window" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\floating_stopwatch_window.xml" qualifiers="" type="layout"/><file name="fragment_receiver_tab" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\fragment_receiver_tab.xml" qualifiers="" type="layout"/><file name="fragment_sender_tab" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\fragment_sender_tab.xml" qualifiers="" type="layout"/><file name="item_color_palette" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_color_palette.xml" qualifiers="" type="layout"/><file name="item_connection" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_connection.xml" qualifiers="" type="layout"/><file name="item_director_info" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_director_info.xml" qualifiers="" type="layout"/><file name="item_director_layout" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_director_layout.xml" qualifiers="" type="layout"/><file name="item_font_file" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_font_file.xml" qualifiers="" type="layout"/><file name="item_font_setting" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_font_setting.xml" qualifiers="" type="layout"/><file name="item_font_size_setting" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_font_size_setting.xml" qualifiers="" type="layout"/><file name="item_layer" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_layer.xml" qualifiers="" type="layout"/><file name="item_letter_spacing" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_letter_spacing.xml" qualifiers="" type="layout"/><file name="item_line_spacing" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_line_spacing.xml" qualifiers="" type="layout"/><file name="item_remote_connection" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_remote_connection.xml" qualifiers="" type="layout"/><file name="item_remote_connection_control" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_remote_connection_control.xml" qualifiers="" type="layout"/><file name="item_remote_receiver" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_remote_receiver.xml" qualifiers="" type="layout"/><file name="item_window_settings" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\item_window_settings.xml" qualifiers="" type="layout"/><file name="layout_text_edit_panel" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\layout_text_edit_panel.xml" qualifiers="" type="layout"/><file name="precision_control_panel" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\precision_control_panel.xml" qualifiers="" type="layout"/><file name="spinner_font_size_dropdown_item" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\spinner_font_size_dropdown_item.xml" qualifiers="" type="layout"/><file name="spinner_font_size_item" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\spinner_font_size_item.xml" qualifiers="" type="layout"/><file name="spinner_letter_spacing_dropdown_item" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\spinner_letter_spacing_dropdown_item.xml" qualifiers="" type="layout"/><file name="spinner_letter_spacing_item" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\spinner_letter_spacing_item.xml" qualifiers="" type="layout"/><file name="spinner_line_spacing_dropdown_item" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\spinner_line_spacing_dropdown_item.xml" qualifiers="" type="layout"/><file name="spinner_line_spacing_item" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\spinner_line_spacing_item.xml" qualifiers="" type="layout"/><file name="spinner_text_alignment_dropdown_item" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\spinner_text_alignment_dropdown_item.xml" qualifiers="" type="layout"/><file name="spinner_text_alignment_item" path="D:\Android\AndroidProject\CastAPP\app\src\main\res\layout\spinner_text_alignment_item.xml" qualifiers="" type="layout"/><file path="D:\Android\AndroidProject\CastAPP\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_blue">#2196F3</color><color name="primary_blue_dark">#1976D2</color><color name="gray_light">#F5F5F5</color><color name="gray_medium">#E0E0E0</color><color name="gray_dark">#757575</color><color name="selected_gray">#E0E0E0</color><color name="selected_gray_dark">#BDBDBD</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color></file><file path="D:\Android\AndroidProject\CastAPP\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">投屏助手</string><string name="send_button">发送</string><string name="receive_button">接收</string><string name="sender_settings">发送端设置</string><string name="receiver_settings">接收端设置</string><string name="add_receiver">添加接收端</string><string name="start_server">启动服务器</string><string name="stop_server">停止服务器</string><string name="ip_address">IP地址</string><string name="port">端口号</string><string name="connection_list">连接列表</string><string name="casting">投屏中</string><string name="connected">已连接</string><string name="disconnected">未连接</string><string name="server_running">服务器运行中</string><string name="permission_denied">权限被拒绝</string><string name="network_error">网络错误</string><string name="invalid_ip">IP地址格式不正确</string><string name="invalid_port">端口号必须在1024-65535之间</string><string name="stopwatch">秒表</string><string name="floating_stopwatch">悬浮秒表</string><string name="stopwatch_running">秒表正在运行中...</string><string name="overlay_permission_required">需要悬浮窗权限才能使用秒表功能</string><string name="stopwatch_started">悬浮秒表已启动</string><string name="stopwatch_start_failed">启动悬浮秒表失败</string><string name="close">关闭</string><string name="connection_id_format">ID: %s</string><string name="connection_count_format">%d个连接</string><string name="bitrate_format">%d Mbps</string><string name="volume_format">%d%%</string><string name="media_audio_volume">媒体音量</string><string name="mic_audio_volume">麦克风音量</string><string name="remote_control">远程被控</string><string name="remote_control_address_format">%s:9999</string><string name="remote_control_server_started">远程被控服务器已启动</string><string name="remote_control_server_stopped">远程被控服务器已停止</string><string name="remote_control_server_start_failed">远程被控服务器启动失败</string><string name="remote_control_manager">遥控管理</string><string name="remote_device_list">远程设备列表</string><string name="device_count_format">%d个设备</string><string name="connect">连接</string><string name="disconnect">断开</string><string name="control">控制</string><string name="add_remote_connection">添加远程连接</string><string name="device_name">设备名称</string><string name="cancel">取消</string><string name="confirm">确定</string><string name="order_number_format">%d. </string><string name="connection_id_parentheses_format">(%s)</string><string formatted="false" name="position_format">位置（%d,%d）</string><string name="scale_format">缩放：%.1f</string><string name="rotation_format">旋转：%d°</string><string name="layer_format">层级：%d</string><string name="window_count_format">（%d个窗口）</string><string name="resolution_info_format">原始: %1$d×%2$d | 当前: %3$d×%4$d</string><string name="resolution_retry_format">分辨率调整失败，正在重试... (%1$d/%2$d)</string><string name="alpha_percentage">%d%%</string></file><file path="D:\Android\AndroidProject\CastAPP\app\src\main\res\values\styles.xml" qualifiers=""><style name="DialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:windowMinWidthMajor">90%</item>
        <item name="android:windowMinWidthMinor">90%</item>
    </style><style name="TabTextStyle" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="SwitchTheme" parent="Theme.AppCompat.Light">
        <item name="colorAccent">#4CAF50</item>
        <item name="android:colorForeground">#42A5F5</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidProject\CastAPP\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidProject\CastAPP\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidProject\CastAPP\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidProject\CastAPP\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>