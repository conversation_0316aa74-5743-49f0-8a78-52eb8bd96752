1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.castapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 基本权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
14-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:8:5-79
14-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:8:22-76
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:9:5-76
15-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:9:22-73
16    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
16-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:10:5-76
16-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:10:22-73
17
18    <!-- 媒体相关权限 -->
19    <uses-permission android:name="android.permission.RECORD_AUDIO" />
19-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:13:5-71
19-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:13:22-68
20    <uses-permission android:name="android.permission.CAMERA" />
20-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:14:5-65
20-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:14:22-62
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:15:5-77
21-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:15:22-74
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
22-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:16:5-94
22-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:16:22-91
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
23-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:17:5-92
23-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:17:22-89
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
24-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:18:5-88
24-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:18:22-85
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
25-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:19:5-89
25-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:19:22-86
26    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
26-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:20:5-77
26-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:20:22-74
27
28    <!-- 存储权限 -->
29    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
29-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:23:5-24:40
29-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:23:22-79
30    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
30-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:25:5-76
30-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:25:22-73
31    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
31-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:26:5-75
31-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:26:22-72
32
33    <!-- 系统窗口权限 -->
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:29:5-78
34-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:29:22-75
35
36    <!-- 硬件特性 -->
37    <uses-feature
37-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:32:5-82
38        android:name="android.hardware.wifi"
38-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:32:19-55
39        android:required="true" />
39-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:32:56-79
40    <uses-feature
40-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:33:5-85
41        android:name="android.hardware.camera"
41-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:33:19-57
42        android:required="false" />
42-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:33:58-82
43    <uses-feature
43-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:34:5-95
44        android:name="android.hardware.screen.landscape"
44-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:34:19-67
45        android:required="false" />
45-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:34:68-92
46    <uses-feature
46-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:35:5-94
47        android:name="android.hardware.screen.portrait"
47-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:35:19-66
48        android:required="false" />
48-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:35:67-91
49
50    <!-- 修改音频设置的权限 -->
51    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
51-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:38:5-80
51-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:38:22-77
52
53    <permission
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
54        android:name="com.example.castapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.example.castapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
58
59    <application
59-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:40:5-100:19
60        android:allowBackup="true"
60-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:41:9-35
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
62        android:debuggable="true"
63        android:extractNativeLibs="false"
64        android:hardwareAccelerated="true"
64-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:45:9-43
65        android:label="CastAPP"
65-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:42:9-32
66        android:largeHeap="true"
66-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:46:9-33
67        android:supportsRtl="true"
67-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:43:9-35
68        android:theme="@style/Theme.AppCompat.Light.NoActionBar"
68-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:44:9-65
69        android:usesCleartextTraffic="true" >
69-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:47:9-44
70        <activity
70-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:50:9-58:20
71            android:name="com.example.castapp.ui.MainActivity"
71-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:51:13-44
72            android:exported="true"
72-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:52:13-36
73            android:screenOrientation="portrait" >
73-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:53:13-49
74            <intent-filter>
74-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:54:13-57:29
75                <action android:name="android.intent.action.MAIN" />
75-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:55:17-69
75-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:55:25-66
76
77                <category android:name="android.intent.category.LAUNCHER" />
77-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:56:17-77
77-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:56:27-74
78            </intent-filter>
79        </activity>
80
81        <service
81-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:60:9-64:63
82            android:name="com.example.castapp.service.CastingService"
82-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:61:13-51
83            android:enabled="true"
83-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:62:13-35
84            android:exported="false"
84-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:63:13-37
85            android:foregroundServiceType="mediaProjection" />
85-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:64:13-60
86        <service
86-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:66:9-70:61
87            android:name="com.example.castapp.service.ReceivingService"
87-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:67:13-53
88            android:enabled="true"
88-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:68:13-35
89            android:exported="false"
89-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:69:13-37
90            android:foregroundServiceType="mediaPlayback" />
90-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:70:13-58
91        <service
91-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:72:9-76:74
92            android:name="com.example.castapp.service.AudioService"
92-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:73:13-49
93            android:enabled="true"
93-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:74:13-35
94            android:exported="false"
94-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:75:13-37
95            android:foregroundServiceType="mediaProjection|microphone" />
95-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:76:13-71
96        <service
96-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:78:9-87:19
97            android:name="com.example.castapp.service.FloatingStopwatchService"
97-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:79:13-61
98            android:enabled="true"
98-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:80:13-35
99            android:exported="false"
99-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:81:13-37
100            android:foregroundServiceType="specialUse" >
100-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:82:13-55
101
102            <!-- Required for "specialUse" type. Describe your use case for app store review -->
103            <property
103-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:84:13-86:42
104                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
104-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:85:17-76
105                android:value="悬浮秒表服务" />
105-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:86:17-39
106        </service>
107        <service
107-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:89:9-98:19
108            android:name="com.example.castapp.service.RemoteReceiverService"
108-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:90:13-58
109            android:enabled="true"
109-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:91:13-35
110            android:exported="false"
110-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:92:13-37
111            android:foregroundServiceType="specialUse" >
111-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:93:13-55
112
113            <!-- Required for "specialUse" type. Describe your use case for app store review -->
114            <property
114-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:84:13-86:42
115                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
115-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:85:17-76
116                android:value="远程接收端控制服务" />
116-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:86:17-39
117        </service>
118
119        <provider
119-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
120            android:name="androidx.startup.InitializationProvider"
120-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
121            android:authorities="com.example.castapp.androidx-startup"
121-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
122            android:exported="false" >
122-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
123            <meta-data
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
124                android:name="androidx.emoji2.text.EmojiCompatInitializer"
124-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
125                android:value="androidx.startup" />
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
126            <meta-data
126-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
127-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
128                android:value="androidx.startup" />
128-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
131                android:value="androidx.startup" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
132        </provider>
133
134        <service
134-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
135            android:name="androidx.room.MultiInstanceInvalidationService"
135-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
136            android:directBootAware="true"
136-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
137            android:exported="false" />
137-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
138
139        <receiver
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
140            android:name="androidx.profileinstaller.ProfileInstallReceiver"
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
141            android:directBootAware="false"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
142            android:enabled="true"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
143            android:exported="true"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
144            android:permission="android.permission.DUMP" >
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
145            <intent-filter>
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
146                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
147            </intent-filter>
148            <intent-filter>
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
149                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
150            </intent-filter>
151            <intent-filter>
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
152                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
153            </intent-filter>
154            <intent-filter>
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
155                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
156            </intent-filter>
157        </receiver>
158    </application>
159
160</manifest>
